//package com.psbc.cpufp.config;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//
//import javax.sql.DataSource;
//
///**
// * 数据源2
// *
// * <AUTHOR>
// */
//@Configuration
//@MapperScan(basePackages = "com.psbc.cpufp.mapper.mappertwo.**", sqlSessionFactoryRef = "twoSqlSessionFactory")
//public class DataSourceTwoConfig {
//
//    /**
//     * 连接池
//     *
//     * @return 返回连接对象
//     */
//    @Bean(value = "twoDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.two")
//    public DataSource druid2() {
//        return new DruidDataSource();
//    }
//
//    /**
//     * 创建数据源二的SqlSessionFactory
//     *
//     * @param dataSource 数据源
//     * @return 数据源二的SqlSessionFactory
//     * @throws Exception 异常
//     */
//    @Bean(name = "twoSqlSessionFactory")
//    public SqlSessionFactory sqlSessionFactory(@Qualifier("twoDataSource") DataSource dataSource) throws Exception {
//        SqlSessionFactoryBean sessionFactoryBean = new SqlSessionFactoryBean();
//        sessionFactoryBean.setDataSource(dataSource);
//        sessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver()
//                .getResources("classpath*:mapperTwo/*.xml"));
//        return sessionFactoryBean.getObject();
//    }
//
//    /**
//     * 创建数据源二的SqlSessionTemplate
//     *
//     * @param sqlSessionFactory sqlSessionFactory
//     * @return SqlSessionTemplate
//     */
//    @Bean(name = "twoSqlSessionTemplate")
//    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("twoSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//}
