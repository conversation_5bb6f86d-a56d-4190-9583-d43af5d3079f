package com.psbc.xmysfzjjg.controller;

import com.psbc.xmysfzjjg.service.ZhbgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 综合办公HTTP接口控制器
 * 替代原有的MyHttpHandler
 */
@RestController
@RequestMapping("/api/admin/xzp")
public class ZhbgController {
    
    private static final Logger logger = LoggerFactory.getLogger(ZhbgController.class);
    
    @Autowired
    private ZhbgService zhbgService;
    
    /**
     * 查询按揭贷款信息接口
     */
    @PostMapping("/queryYxjfLsxdye")
    public String queryYxjfLsxdye(@RequestBody String requestBody) {
        logger.info("--------------------进入请求综合办公获取按揭贷款信息接口------------------------------------------");
        logger.info("请求综合办公参数: {}", requestBody);
        
        try {
            String response = zhbgService.sendZhbgRequest(requestBody, "30001");
            logger.info("综合办公返回结果: {}", response);
            return response;
            
        } catch (Exception e) {
            logger.error("处理按揭贷款信息查询请求时发生异常", e);
            return "{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}";
        }
    }
    
    /**
     * 查询监管账户信息接口
     */
    @PostMapping("/queryJgzhInfo")
    public String queryJgzhInfo(@RequestBody String requestBody) {
        logger.info("--------------------进入请求综合办公查询监管账户信息接口------------------------------------------");
        logger.info("请求综合办公参数: {}", requestBody);
        
        try {
            String response = zhbgService.sendZhbgRequest(requestBody, "20006");
            logger.info("综合办公返回结果: {}", response);
            return response;
            
        } catch (Exception e) {
            logger.error("处理监管账户信息查询请求时发生异常", e);
            return "<?xml version=\"1.0\" encoding=\"utf-8\"?><content><head><statecode>0</statecode><msg>处理异常: " + e.getMessage() + "</msg></head></content>";
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public String health() {
        return "{\"status\":\"ok\",\"service\":\"xmysfzjjg\"}";
    }
}
