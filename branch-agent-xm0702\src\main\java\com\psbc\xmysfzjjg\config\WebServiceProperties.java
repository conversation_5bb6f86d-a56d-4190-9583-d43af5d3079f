package com.psbc.xmysfzjjg.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * WebService配置属性类
 */
@Component
@ConfigurationProperties(prefix = "xmysfzjjg.webservice")
public class WebServiceProperties {
    
    /**
     * 客户端配置
     */
    private Client client = new Client();

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public static class Client {
        /**
         * 委托方WebService地址
         */
        private String address = "https://*************:8088/JYDJService/JYDJService1.asmx?wsdl";

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }
    }
}
