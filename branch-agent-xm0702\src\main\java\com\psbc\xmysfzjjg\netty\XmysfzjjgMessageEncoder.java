package com.psbc.xmysfzjjg.netty;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * xmysfzjjg消息编码器
 * 将消息编码为前6字节长度 + 消息体的格式
 */
public class XmysfzjjgMessageEncoder extends MessageToByteEncoder<String> {
    
    private static final Logger logger = LoggerFactory.getLogger(XmysfzjjgMessageEncoder.class);
    
    @Override
    protected void encode(ChannelHandlerContext ctx, String msg, ByteBuf out) throws Exception {
        if (msg == null) {
            return;
        }
        
        byte[] messageBytes = msg.getBytes("UTF-8");
        int messageLength = messageBytes.length;
        
        // 格式化长度为6位字符串（左补0）
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes("UTF-8");
        
        logger.debug("编码消息: 长度={}, 内容={}", messageLength, msg);
        
        // 写入长度字段（6字节）
        out.writeBytes(lengthBytes);
        
        // 写入消息体
        out.writeBytes(messageBytes);
    }
}
