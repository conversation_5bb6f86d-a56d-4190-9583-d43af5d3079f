package com.psbc.xmysfzjjg;

import com.psbc.cpufp.BranchAgentApplication;
import com.psbc.xmysfzjjg.config.XmysfzjjgProperties;
import com.psbc.xmysfzjjg.config.ZhbgProperties;
import com.psbc.xmysfzjjg.service.MessageProcessService;
import com.psbc.xmysfzjjg.util.ConfigService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * xmysfzjjg项目集成测试
 */
@SpringBootTest(classes = BranchAgentApplication.class)
@ActiveProfiles("local")
public class XmysfzjjgApplicationTest {
    
    @Autowired
    private XmysfzjjgProperties xmysfzjjgProperties;
    
    @Autowired
    private ZhbgProperties zhbgProperties;
    
    @Autowired
    private ConfigService configService;
    
    @Autowired
    private MessageProcessService messageProcessService;
    
    @Test
    public void testConfigurationProperties() {
        // 测试基础配置
        assertNotNull(xmysfzjjgProperties);
        assertEquals("127.0.0.1", xmysfzjjgProperties.getIp());
        assertEquals(Integer.valueOf(9702), xmysfzjjgProperties.getPort());
        assertEquals(Integer.valueOf(8888), xmysfzjjgProperties.getWgPort());
        
        // 测试综合办公配置
        assertNotNull(zhbgProperties);
        assertEquals("api/admin/xzp/queryYxjfLsxdye", zhbgProperties.getUrl30001());
        assertEquals("api/admin/xzp/queryJgzhInfo", zhbgProperties.getUrl20006());
    }
    
    @Test
    public void testConfigService() {
        // 测试ConfigService
        assertNotNull(configService);
        assertEquals("127.0.0.1", configService.getXmysfzjjgValue("IP"));
        assertEquals("9702", configService.getXmysfzjjgValue("PORT"));
        assertEquals("api/admin/xzp/queryYxjfLsxdye", configService.getZhbgValue("url30001"));
    }
    
    @Test
    public void testMessageProcessService() {
        // 测试消息处理服务
        assertNotNull(messageProcessService);
        
        // 测试JSON消息处理（模拟）
        String jsonMessage = "{\"serviceno\":\"30001\",\"data\":\"test\"}";
        try {
            // 这里只是测试服务是否能正常初始化，实际的网络调用需要mock
            assertNotNull(messageProcessService);
        } catch (Exception e) {
            // 预期可能会有网络异常，这是正常的
        }
    }
    
    @Test
    public void testApplicationContext() {
        // 测试Spring上下文是否正常加载
        assertNotNull(xmysfzjjgProperties);
        assertNotNull(zhbgProperties);
        assertNotNull(configService);
        assertNotNull(messageProcessService);
    }
}
