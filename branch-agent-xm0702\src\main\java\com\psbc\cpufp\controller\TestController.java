//package com.psbc.cpufp.controller;
//
//import com.alibaba.fastjson.JSON;
//import cn.hutool.json.JSONObject;
//import com.psbc.cpufp.common.cache.redis.RedisCacheUtil;
//import com.psbc.cpufp.common.cache.service.TableInfoInterface;
//import com.psbc.cpufp.common.cache.utils.FuzzyKeyOfTableInfoUtil;
//import com.psbc.cpufp.common.enumcomm.eum.ResponseCodeEnum;
//import com.psbc.cpufp.common.serial.util.SerialNumberGenerateUtils;
//import com.psbc.cpufp.entity.enu.tableinfoenum.FrontTxCodeInfo4RedisEnum;
//import com.psbc.cpufp.exception.BusinessException;
//import com.psbc.cpufp.exception.SystemException;
//import com.psbc.cpufp.faced.RestTemplateFacade;
//import com.psbc.cpufp.feign.NorthOgFeign;
//import feign.Request;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.util.HashMap;
//import java.util.Set;
//import java.util.UUID;
//import java.util.concurrent.Callable;
//import java.util.concurrent.TimeUnit;
//
///**
// * 测试基础功能专用
// *
// * <AUTHOR>
// */
//@Slf4j
//@RestController
//public class TestController {
//
//
//    private static final Logger log = LoggerFactory.getLogger(TestController.class);
//
//    /**
//     * redis
//     */
//    @Autowired
//    private RedisCacheUtil redisCacheUtil;
//
//    /**
//     * 文件分割符
//     */
//    private static final String SPLIT_CHAR = ".";
//    /**
//     * 文件名不能包含的特殊字符
//     */
//    private static final String REJECT_FILENAME = "/:*?\"<>|";
//    /**
//     * 远程服务
//     */
//    @Autowired
//    private NorthOgFeign northOgFeign;
//    @Autowired
//    private RestTemplateFacade restTemplateFacade;
//    /**
//     * 连接超时时间
//     */
//    @Value("${cpufp.http-client.connectTimeOut:2000}")
//    private String connectTimeOut;
//    /**
//     * 响应超时时间：
//     * 配置可根据发布交易时修改：小于接入网关的时间，大于接出网关的超时时间
//     */
//    @Value("${cpufp.http-client.responseTimeOut:5000}")
//    private String responseTimeOut;
//
//    /**
//     * redis删除key的工具类
//     */
//    @Autowired
//    private FuzzyKeyOfTableInfoUtil fuzzyKeyOfTableInfoUtil;
//
//    @GetMapping("test11")
//    public Callable<String> test111() {
//        return () -> UUID.randomUUID().toString() + "dskjfisdjfieji";
//    }
//
//
//    /**
//     * Redis 删除
//     *
//     * @param frontTxCode frontTxCode
//     */
//    @PostMapping("/test/delete")
//    public void redisDelete(@RequestBody String frontTxCode) {
//        HashMap<TableInfoInterface, Object> tableEnmObjectHashMap = new HashMap<>();
//        // 组装入参（可变参数）
//        tableEnmObjectHashMap.put(FrontTxCodeInfo4RedisEnum.FRONT_TX_CODE, frontTxCode);
//        tableEnmObjectHashMap.put(FrontTxCodeInfo4RedisEnum.FLAG, "1");
//        Set<String> keys = fuzzyKeyOfTableInfoUtil.getRedisKeys(FrontTxCodeInfo4RedisEnum.FLAG.getTableName(), tableEnmObjectHashMap);
//        log.info("redis key:", JSON.toJSONString(keys));
//        fuzzyKeyOfTableInfoUtil.delete(FrontTxCodeInfo4RedisEnum.FLAG.getTableName(), tableEnmObjectHashMap);
//    }
//
//    /**
//     * 远程调用north-og-gw
//     * 如果需要设置超时时间，可以在配置文件中设置，每个接口都可以自定义超时时间
//     * （不同的接口可以设置不同的超时）
//     *
//     * @param jsonObject body
//     * @return 远程调用结果
//     */
//    @RequestMapping("/getNorth")
//    public String feignNorth(@RequestBody String jsonObject) {
//        // 动态设置超时时间
//        final Request.Options options = new Request.Options(
//                // 连接超时时间
//                Long.parseLong(connectTimeOut),
//                TimeUnit.MILLISECONDS,
//                // 响应超时时间
//                Long.parseLong(responseTimeOut),
//                TimeUnit.MILLISECONDS, true);
//        // 默认值connectTime:10s,readTime:60s
//        // 北接出标准模式接口
//        String test = northOgFeign.getNorthTrans(jsonObject, options);
//        log.info(test);
//        return test;
//    }
//
//    /**
//     * 调用北接出示例
//     *
//     * @param jsonObject 请求参数
//     * @return 返回的结果
//     */
//    @PostMapping("/getTransNorth")
//    public String feignTransNorth(@RequestBody String jsonObject) {
//        // 动态设置超时时间
//        final Request.Options options = new Request.Options(
//                // 连接超时时间
//                Long.parseLong(connectTimeOut),
//                TimeUnit.MILLISECONDS,
//                // 响应超时时间
//                Long.parseLong(responseTimeOut),
//                TimeUnit.MILLISECONDS, true);
//        log.info("请求参数为：{}", jsonObject);
//        return northOgFeign.getNorthTrans(jsonObject, options);
//    }
//
//    /**
//     * 测试业务异常
//     *
//     * @param flag 标识是否抛出异常
//     * @return 结果
//     */
//    @RequestMapping("/simulation/business")
//    public boolean simulationBusiness(@RequestHeader("flag") boolean flag) {
//        if (flag) {
//            throw new BusinessException(ResponseCodeEnum.CERT_INSERT_EXISTS_ERROR);
//        }
//        return false;
//    }
//
//    /**
//     * 测试系统异常
//     *
//     * @param flag 标识是否抛出异常
//     * @return 结果
//     */
//    @RequestMapping("/simulation/system")
//    public boolean simulationSystem(@RequestHeader("flag") boolean flag) {
//        if (flag) {
//            throw new SystemException(ResponseCodeEnum.CERT_START_OR_STOP_ERROR);
//        }
//        return false;
//    }
//
//    /**
//     * restTemplate远程调用北接出透传
//     *
//     * @param values 请求参数
//     * @return 调用结果
//     */
//    @RequestMapping("/getNorthTrans")
//    public String getNorthTrans(
//            @RequestBody String values, @RequestHeader int connectTimeOut,
//            @RequestHeader int responseTimeOut) {
//        HttpHeaders headers = new HttpHeaders();
//        ResponseEntity<String> stringResponseEntity = restTemplateFacade.postNorthTrans(headers, values, String.class,
//                connectTimeOut, responseTimeOut);
//        return stringResponseEntity.getBody();
//    }
//
//    @PostMapping("/base/globalBusiTrackNo/test")
//    public String globalBusiTrackNoTest() {
//        return SerialNumberGenerateUtils.generate();
//    }
//
//
//    @PostMapping("/jsontest")
//    private JSONObject test(HttpServletRequest request, HttpServletResponse res) {
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.set("fileId", "sdfsdfsdfwe");
//        return jsonObject;
//    }
//}
//
