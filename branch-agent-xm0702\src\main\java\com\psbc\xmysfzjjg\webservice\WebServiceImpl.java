package com.psbc.xmysfzjjg.webservice;

import com.psbc.xmysfzjjg.service.WlHostService;
import com.psbc.xmysfzjjg.util.ConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;

/**
 * WebService实现类
 * 整合原有WebServiceImpl的功能
 */
@Service
@WebService(serviceName = "IWebServiceService", 
           targetNamespace = "http://webservice.psbc.com/",
           endpointInterface = "com.psbc.xmysfzjjg.webservice.IWebService")
public class WebServiceImpl implements IWebService {
    
    private static final Logger logger = LoggerFactory.getLogger(WebServiceImpl.class);
    
    @Autowired
    private ConfigService configService;
    
    @Autowired
    private WlHostService wlHostService;
    
    @Override
    @WebMethod
    @WebResult(targetNamespace = "http://webservice.psbc.com/")
    public String sayHello(@WebParam(targetNamespace = "http://webservice.psbc.com/") String name) {
        logger.info("sayHello方法被调用，参数: {}", name);
        return "Hello, " + name + "!";
    }
    
    @Override
    @WebMethod
    @WebResult(targetNamespace = "http://webservice.psbc.com/")
    public String Execute(@WebParam(targetNamespace = "http://webservice.psbc.com/") String bankId, 
                         @WebParam(targetNamespace = "http://webservice.psbc.com/") String inParameter) {
        
        logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>接收委托方webservice报文开始>>>>>>>>>>>>>>>>>>>>>>>>>");
        logger.info("BankID: {}, 报文inParameter: {}", bankId, inParameter != null ? inParameter.trim() : "null");
        
        String response = null;
        
        try {
            long startTime = System.nanoTime();
            logger.info("报文发往外联系统开始>>>");
            
            // 调用外联主机服务
            response = wlHostService.sendToWlHost(inParameter != null ? inParameter.trim() : "");
            
            long endTime = System.nanoTime();
            long duration = (endTime - startTime) / 1000L / 1000L;
            
            logger.info("报文发往外联系统结束>>>");
            logger.info("外联系统返回报文耗时: {} 毫秒", duration);
            logger.info("外联系统返回报文长度: {}", response != null ? response.length() : 0);
            logger.info("外联系统返回报文: [{}]", response);
            
        } catch (Exception e) {
            logger.error("WebService Execute方法执行异常", e);
            // 返回默认错误响应
            response = configService.getXmysfzjjgValue("MESSAGE");
        }
        
        logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>委托方webservice报文处理结束>>>>>>>>>>>>>>>>>>>>>>>>>");
        return response;
    }
}
