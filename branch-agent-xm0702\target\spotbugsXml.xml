
<BugCollection sequence='0' release='' analysisTimestamp='*************' version='3.1.10' timestamp='*************'><Project projectName='branch-agent'><Jar>C:\workspace\bank\branch-agent-xm0417\target\classes</Jar><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\bfes-agent-comm\1.0.11.RELEASE\bfes-agent-comm-1.0.11.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.13.5\jackson-dataformat-xml-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\woodstox\woodstox-core\6.4.0\woodstox-core-6.4.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\net\sf\json-lib\json-lib\2.4\json-lib-2.4-jdk15.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\net\sf\ezmorph\ezmorph\1.0.6\ezmorph-1.0.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-logging\commons-logging\1.2\commons-logging-1.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\xom\xom\1.2.5\xom-1.2.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\feign-httpclient\11.10\feign-httpclient-11.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\httpcomponents\httpcore\4.4.13\httpcore-4.4.13.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-codec\commons-codec\1.15\commons-codec-1.15.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\tomcat\embed\tomcat-embed-core\9.0.73\tomcat-embed-core-9.0.73.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\tomcat\tomcat-annotations-api\9.0.73\tomcat-annotations-api-9.0.73.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common-response-enum\1.0.12.RELEASE\common-response-enum-1.0.12.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\cn\hutool\hutool-all\5.7.20\hutool-all-5.7.20.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-validation\2.7.10\spring-boot-starter-validation-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter\2.7.10\spring-boot-starter-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot\2.7.10\spring-boot-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-autoconfigure\2.7.10\spring-boot-autoconfigure-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-logging\2.7.10\spring-boot-starter-logging-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-core\5.3.26\spring-core-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-jcl\5.3.26\spring-jcl-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\tomcat\embed\tomcat-embed-el\9.0.73\tomcat-embed-el-9.0.73.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-starter-openfeign\3.1.6\spring-cloud-starter-openfeign-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-starter\3.1.6\spring-cloud-starter-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\security\spring-security-rsa\1.0.11.RELEASE\spring-security-rsa-1.0.11.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\bouncycastle\bcpkix-jdk15on\1.69\bcpkix-jdk15on-1.69.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\bouncycastle\bcutil-jdk15on\1.69\bcutil-jdk15on-1.69.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-openfeign-core\3.1.6\spring-cloud-openfeign-core-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-aop\2.7.10\spring-boot-starter-aop-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-web\5.3.26\spring-web-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-beans\5.3.26\spring-beans-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-commons\3.1.6\spring-cloud-commons-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\security\spring-security-crypto\5.7.7\spring-security-crypto-5.7.7.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\feign-core\11.10\feign-core-11.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\github\openfeign\feign-slf4j\11.10\feign-slf4j-11.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-web\2.7.10\spring-boot-starter-web-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-json\2.7.10\spring-boot-starter-json-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-tomcat\2.7.10\spring-boot-starter-tomcat-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.73\tomcat-embed-websocket-9.0.73.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-webmvc\5.3.26\spring-webmvc-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-aop\5.3.26\spring-aop-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-context\5.3.26\spring-context-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-expression\5.3.26\spring-expression-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\retry\spring-retry\1.3.4\spring-retry-1.3.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-actuator\2.7.10\spring-boot-starter-actuator-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-actuator-autoconfigure\2.7.10\spring-boot-actuator-autoconfigure-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-actuator\2.7.10\spring-boot-actuator-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\micrometer\micrometer-core\1.9.9\micrometer-core-1.9.9.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common.log.frame\1.0.2.RELEASE\common.log.frame-1.0.2.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\yaml\snakeyaml\1.31\snakeyaml-1.31.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\commons\commons-lang3\3.11\commons-lang3-3.11.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\codehaus\janino\janino\3.1.9\janino-3.1.9.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\codehaus\janino\commons-compiler\3.1.9\commons-compiler-3.1.9.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\postgresql\postgresql\42.2.26\postgresql-42.2.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\alibaba\druid\1.2.8\druid-1.2.8.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common.jasypt.decrypt\1.0.2.RELEASE\common.jasypt.decrypt-1.0.2.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\bouncycastle\bcprov-jdk15on\1.69\bcprov-jdk15on-1.69.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\cloud\spring-cloud-context\3.1.6\spring-cloud-context-3.1.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\soma\job\soma_client_java\1.0.6.1.RELEASE\soma_client_java-1.0.6.1.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-netty\1.50.0\grpc-netty-1.50.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-codec-http2\4.1.90.Final\netty-codec-http2-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-common\4.1.90.Final\netty-common-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-buffer\4.1.90.Final\netty-buffer-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-transport\4.1.90.Final\netty-transport-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-resolver\4.1.90.Final\netty-resolver-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-codec\4.1.90.Final\netty-codec-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-handler\4.1.90.Final\netty-handler-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-codec-http\4.1.90.Final\netty-codec-http-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\guava\guava\31.1-android\guava-31.1-android.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\netty\netty-transport-native-unix-common\4.1.90.Final\netty-transport-native-unix-common-4.1.90.Final.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-core\1.50.0\grpc-core-1.50.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-protobuf\1.56.0\grpc-protobuf-1.56.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\google\api\grpc\proto-google-common-protos\2.17.0\proto-google-common-protos-2.17.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-protobuf-lite\1.56.0\grpc-protobuf-lite-1.56.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\grpc\grpc-stub\1.56.0\grpc-stub-1.56.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\github\pagehelper\pagehelper-spring-boot-starter\1.4.1\pagehelper-spring-boot-starter-1.4.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.1\mybatis-spring-boot-starter-2.2.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-jdbc\2.7.10\spring-boot-starter-jdbc-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-jdbc\5.3.26\spring-jdbc-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.1\mybatis-spring-boot-autoconfigure-2.2.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\mybatis\mybatis\3.5.9\mybatis-3.5.9.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.4.1\pagehelper-spring-boot-autoconfigure-1.4.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\github\pagehelper\pagehelper\5.3.1\pagehelper-5.3.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\github\jsqlparser\jsqlparser\4.2\jsqlparser-4.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\commons-io\commons-io\2.7\commons-io-2.7.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common.serial.api\1.0.2.RELEASE\common.serial.api-1.0.2.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp\bfes\common.cache.client\1.0.7.RELEASE\common.cache.client-1.0.7.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\boot\spring-boot-starter-data-redis\2.7.10\spring-boot-starter-data-redis-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\data\spring-data-redis\2.7.10\spring-data-redis-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\data\spring-data-keyvalue\2.7.10\spring-data-keyvalue-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\data\spring-data-commons\2.7.10\spring-data-commons-2.7.10.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-tx\5.3.26\spring-tx-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-oxm\5.3.26\spring-oxm-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\springframework\spring-context-support\5.3.26\spring-context-support-5.3.26.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\io\projectreactor\reactor-core\3.4.28\reactor-core-3.4.28.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\redis\clients\jedis\3.8.0\jedis-3.8.0.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\org\projectlombok\lombok\1.18.18\lombok-1.18.18.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar</AuxClasspathEntry><AuxClasspathEntry>C:\workspace\bank\repo_cpufp\com\psbc\cpufp-security-sdk\1.0.6.RELEASE\cpufp-security-sdk-1.0.6.RELEASE.jar</AuxClasspathEntry><SrcDir>C:\workspace\bank\branch-agent-xm0417\src\main\java</SrcDir><SrcDir>C:\workspace\bank\branch-agent-xm0417\target\generated-sources\annotations</SrcDir><WrkDir>C:\workspace\bank\branch-agent-xm0417\target</WrkDir></Project><Errors missingClasses='0' errors='1'><Error><ErrorMessage>Unable to read filter: C:\workspace\bank\branch-agent-xm0417\target\sourceFiles.xml : 文件提前结束。</ErrorMessage><Exception>java.io.IOException: 文件提前结束。</Exception><StackTrace>edu.umd.cs.findbugs.filter.Filter.&lt;init&gt;(Filter.java:134)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs.configureFilter(FindBugs.java:512)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs2.addFilter(FindBugs2.java:372)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs2.configureFilters(FindBugs2.java:500)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs2.setUserPreferences(FindBugs2.java:473)</StackTrace><StackTrace>edu.umd.cs.findbugs.TextUICommandLine.configureEngine(TextUICommandLine.java:672)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs.processCommandLine(FindBugs.java:365)</StackTrace><StackTrace>edu.umd.cs.findbugs.FindBugs2.main(FindBugs2.java:1175)</StackTrace></Error></Errors><FindBugsSummary num_packages='11' total_classes='21' total_size='441' clock_seconds='5.92' referenced_classes='140' vm_version='25.391-b13' total_bugs='0' java_version='1.8.0_391' gc_seconds='0.10' alloc_mbytes='455.50' cpu_seconds='23.27' peak_mbytes='198.25' timestamp='Sat, 28 Jun 2025 00:51:22 +0800'><FileStats path='com/psbc/cpufp/BranchAgentApplication.java' size='6' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/config/FpSmConfig.java' size='28' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/constant/KeyModelConstant.java' size='6' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/controller/FpController.java' size='38' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/indto/BankInfoCheckDto.java' size='23' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/indto/DgAccountQueryDto.java' size='19' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/indto/HeadDto.java' size='31' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/indto/RootXmlDto.java' size='14' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/outdto/ResponseBankInfoCheckDto.java' size='39' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/outdto/ResponseDgAccountQueryDto.java' size='19' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/outdto/ResponseHeadDto.java' size='39' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/dto/outdto/ResponseRootXmlDto.java' size='14' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/enu/ExploreTypeEum.java' size='27' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/entity/enu/responsecodeenum/GlobalReponseCodeEnum.java' size='25' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/exception/GlobalException.java' size='4' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/exception/handler/GlobalExceptionHandler.java' size='18' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/util/MacUtil.java' size='7' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/util/SM2KeyPairUtil.java' size='65' bugCount='0'></FileStats><FileStats path='com/psbc/cpufp/util/XmlUtils.java' size='19' bugCount='0'></FileStats><PackageStats package='com.psbc.cpufp' total_bugs='0' total_size='6' total_types='1'><ClassStats bugs='0' size='6' interface='false' sourceFile='BranchAgentApplication.java' class='com.psbc.cpufp.BranchAgentApplication'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.config' total_bugs='0' total_size='28' total_types='1'><ClassStats bugs='0' size='28' interface='false' sourceFile='FpSmConfig.java' class='com.psbc.cpufp.config.FpSmConfig'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.constant' total_bugs='0' total_size='6' total_types='1'><ClassStats bugs='0' size='6' interface='false' sourceFile='KeyModelConstant.java' class='com.psbc.cpufp.constant.KeyModelConstant'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.controller' total_bugs='0' total_size='38' total_types='3'><ClassStats bugs='0' size='30' interface='false' sourceFile='FpController.java' class='com.psbc.cpufp.controller.FpController'></ClassStats><ClassStats bugs='0' size='4' interface='false' sourceFile='FpController.java' class='com.psbc.cpufp.controller.FpController$1'></ClassStats><ClassStats bugs='0' size='4' interface='false' sourceFile='FpController.java' class='com.psbc.cpufp.controller.FpController$2'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.entity.dto.indto' total_bugs='0' total_size='87' total_types='4'><ClassStats bugs='0' size='23' interface='false' sourceFile='BankInfoCheckDto.java' class='com.psbc.cpufp.entity.dto.indto.BankInfoCheckDto'></ClassStats><ClassStats bugs='0' size='19' interface='false' sourceFile='DgAccountQueryDto.java' class='com.psbc.cpufp.entity.dto.indto.DgAccountQueryDto'></ClassStats><ClassStats bugs='0' size='31' interface='false' sourceFile='HeadDto.java' class='com.psbc.cpufp.entity.dto.indto.HeadDto'></ClassStats><ClassStats bugs='0' size='14' interface='false' sourceFile='RootXmlDto.java' class='com.psbc.cpufp.entity.dto.indto.RootXmlDto'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.entity.dto.outdto' total_bugs='0' total_size='111' total_types='4'><ClassStats bugs='0' size='39' interface='false' sourceFile='ResponseBankInfoCheckDto.java' class='com.psbc.cpufp.entity.dto.outdto.ResponseBankInfoCheckDto'></ClassStats><ClassStats bugs='0' size='19' interface='false' sourceFile='ResponseDgAccountQueryDto.java' class='com.psbc.cpufp.entity.dto.outdto.ResponseDgAccountQueryDto'></ClassStats><ClassStats bugs='0' size='39' interface='false' sourceFile='ResponseHeadDto.java' class='com.psbc.cpufp.entity.dto.outdto.ResponseHeadDto'></ClassStats><ClassStats bugs='0' size='14' interface='false' sourceFile='ResponseRootXmlDto.java' class='com.psbc.cpufp.entity.dto.outdto.ResponseRootXmlDto'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.entity.enu' total_bugs='0' total_size='27' total_types='1'><ClassStats bugs='0' size='27' interface='false' sourceFile='ExploreTypeEum.java' class='com.psbc.cpufp.entity.enu.ExploreTypeEum'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.entity.enu.responsecodeenum' total_bugs='0' total_size='25' total_types='1'><ClassStats bugs='0' size='25' interface='false' sourceFile='GlobalReponseCodeEnum.java' class='com.psbc.cpufp.entity.enu.responsecodeenum.GlobalReponseCodeEnum'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.exception' total_bugs='0' total_size='4' total_types='1'><ClassStats bugs='0' size='4' interface='false' sourceFile='GlobalException.java' class='com.psbc.cpufp.exception.GlobalException'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.exception.handler' total_bugs='0' total_size='18' total_types='1'><ClassStats bugs='0' size='18' interface='false' sourceFile='GlobalExceptionHandler.java' class='com.psbc.cpufp.exception.handler.GlobalExceptionHandler'></ClassStats></PackageStats><PackageStats package='com.psbc.cpufp.util' total_bugs='0' total_size='91' total_types='3'><ClassStats bugs='0' size='7' interface='false' sourceFile='MacUtil.java' class='com.psbc.cpufp.util.MacUtil'></ClassStats><ClassStats bugs='0' size='65' interface='false' sourceFile='SM2KeyPairUtil.java' class='com.psbc.cpufp.util.SM2KeyPairUtil'></ClassStats><ClassStats bugs='0' size='19' interface='false' sourceFile='XmlUtils.java' class='com.psbc.cpufp.util.XmlUtils'></ClassStats></PackageStats><FindBugsProfile><ClassProfile avgMicrosecondsPerInvocation='978' totalMilliseconds='1185' name='edu.umd.cs.findbugs.classfile.engine.ClassDataAnalysisEngine' maxMicrosecondsPerInvocation='3920' standardDeviationMicrosecondsPerInvocation='410' invocations='1212'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='753' totalMilliseconds='905' name='edu.umd.cs.findbugs.classfile.engine.ClassInfoAnalysisEngine' maxMicrosecondsPerInvocation='30750' standardDeviationMicrosecondsPerInvocation='2025' invocations='1202'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='1852' totalMilliseconds='259' name='edu.umd.cs.findbugs.detect.FieldItemSummary' maxMicrosecondsPerInvocation='28685' standardDeviationMicrosecondsPerInvocation='3763' invocations='140'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='1478' totalMilliseconds='220' name='edu.umd.cs.findbugs.classfile.engine.bcel.MethodGenFactory' maxMicrosecondsPerInvocation='201624' standardDeviationMicrosecondsPerInvocation='16452' invocations='149'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='1508' totalMilliseconds='211' name='edu.umd.cs.findbugs.detect.FindNoSideEffectMethods' maxMicrosecondsPerInvocation='31398' standardDeviationMicrosecondsPerInvocation='3629' invocations='140'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='392' totalMilliseconds='182' name='edu.umd.cs.findbugs.OpcodeStack$JumpInfoFactory' maxMicrosecondsPerInvocation='31538' standardDeviationMicrosecondsPerInvocation='1594' invocations='466'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='700' totalMilliseconds='104' name='edu.umd.cs.findbugs.ba.npe.NullDerefAndRedundantComparisonFinder' maxMicrosecondsPerInvocation='10673' standardDeviationMicrosecondsPerInvocation='1460' invocations='149'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='89' totalMilliseconds='103' name='edu.umd.cs.findbugs.util.TopologicalSort' maxMicrosecondsPerInvocation='3154' standardDeviationMicrosecondsPerInvocation='198' invocations='1156'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='645' totalMilliseconds='96' name='edu.umd.cs.findbugs.classfile.engine.bcel.IsNullValueDataflowFactory' maxMicrosecondsPerInvocation='8948' standardDeviationMicrosecondsPerInvocation='1379' invocations='149'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='570' totalMilliseconds='85' name='edu.umd.cs.findbugs.classfile.engine.bcel.TypeDataflowFactory' maxMicrosecondsPerInvocation='23529' standardDeviationMicrosecondsPerInvocation='2035' invocations='149'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='584' totalMilliseconds='81' name='edu.umd.cs.findbugs.detect.NoteDirectlyRelevantTypeQualifiers' maxMicrosecondsPerInvocation='16320' standardDeviationMicrosecondsPerInvocation='1602' invocations='140'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='539' totalMilliseconds='80' name='edu.umd.cs.findbugs.classfile.engine.bcel.CFGFactory' maxMicrosecondsPerInvocation='25487' standardDeviationMicrosecondsPerInvocation='2124' invocations='149'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='331' totalMilliseconds='79' name='edu.umd.cs.findbugs.classfile.engine.bcel.JavaClassAnalysisEngine' maxMicrosecondsPerInvocation='25638' standardDeviationMicrosecondsPerInvocation='1747' invocations='241'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='497' totalMilliseconds='74' name='edu.umd.cs.findbugs.classfile.engine.bcel.UnconditionalValueDerefDataflowFactory' maxMicrosecondsPerInvocation='8141' standardDeviationMicrosecondsPerInvocation='993' invocations='149'></ClassProfile><ClassProfile avgMicrosecondsPerInvocation='453' totalMilliseconds='67' name='edu.umd.cs.findbugs.classfile.engine.bcel.ValueNumberDataflowFactory' maxMicrosecondsPerInvocation='19629' standardDeviationMicrosecondsPerInvocation='1646' invocations='149'></ClassProfile></FindBugsProfile></FindBugsSummary><ClassFeatures></ClassFeatures><History></History></BugCollection>