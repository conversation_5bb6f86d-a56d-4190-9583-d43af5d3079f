//package com.psbc.cpufp.util;
//
//import cn.hutool.core.util.HexUtil;
//import cn.hutool.crypto.Mode;
//import cn.hutool.crypto.Padding;
//import cn.hutool.crypto.SmUtil;
//import cn.hutool.crypto.asymmetric.KeyType;
//import cn.hutool.crypto.asymmetric.SM2;
//import cn.hutool.crypto.symmetric.SM4;
//import lombok.extern.slf4j.Slf4j;
//import org.bouncycastle.jce.provider.BouncyCastleProvider;
//import org.bouncycastle.util.encoders.Hex;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.crypto.SecretKey;
//import javax.xml.bind.DatatypeConverter;
//import java.io.UnsupportedEncodingException;
//import java.nio.charset.StandardCharsets;
//import java.security.Security;
//
///**
// * <AUTHOR> Ye
// * @version 1.0.0
// * @title EncryptAndDecryptTools
// * @description 警银通加解密工具 —— 与公安数据交流的加解密
// * @create 2025/3/25 11:18
// **/
//@Slf4j
//@Component
//public class EncryptAndDecryptTools {
//
//    @Value("${jyt.userCert.userCertPublicKey}")
//    private String publicKeyValue;
//
//    @Value("${jyt.userCert.userCertPrivateKey}")
//    private String privateKeyValue;
//
//    private static final Logger log = LoggerFactory.getLogger(EncryptAndDecryptTools.class);
//
//    /**
//     * 加密
//     *
//     * @param jsonPlainText 加密内容
//     * @return 加密后的实体
//     */
//    public BodyModel encryptMyDataToBankByPublicKey(String jsonPlainText) throws UnsupportedEncodingException {
//        // 1.生成随机sm4的secretKey
//        String sm4Key = randSm4SecretKey();
//        byte[] sm4Bytes = DatatypeConverter.parseHexBinary(sm4Key);
//        // 2.基于银行的公钥对sm4进行sm2加密
//        String sm4Cipher = sm2EncryptByPublicKey(publicKeyValue, sm4Bytes);
//        // 3.基于sm4对数据报文进行sm4加密
//        String dataCipher = sm4Encrypt(sm4Bytes, jsonPlainText);
//        BodyModel bodyModel = new BodyModel();
//
//        bodyModel.setContent(dataCipher);
//        bodyModel.setSignatureValue(sm4Cipher);
//        return bodyModel;
//    }
//
//    /**
//     * 解密
//     *
//     * @param bodyModel 需要解密的数据
//     * @return 解密后的数据
//     */
//    public String decryptData(BodyModel bodyModel) {
//        String dataPlainText = decryptBankData(privateKeyValue, bodyModel);
//        // 首字母大写转小写
//        return jsonToLower(dataPlainText);
//    }
//
//    private String jsonToLower(String str) {
//        if (str == null || str.isEmpty()) {
//            return str;
//        }
//        return str.substring(0, 1).toUpperCase() + str.substring(1);
//    }
//
//    private String decryptBankData(String privateKey, BodyModel model) {
//        String smCipher = model.getSignatureValue();
//        String dataCipher = model.getContent();
//        return decryptBankData(privateKey, smCipher, dataCipher);
//    }
//
//    private String decryptBankData(String privateKey, String sm4Cipher, String dataCipher) {
//        // 1.使用我方sm2私钥对sm4密文进行解密
//        // 银行sm4密文需手动在前面补04
//        // String prefix = "04";
//        // int lastInger = 2;
//        // if (!prefix.equals(sm4Cipher.substring(0, lastInger))) {
//        //     sm4Cipher = prefix + sm4Cipher;
//        // }
//
//        byte[] sm4Byte = sm2DecryptByte(privateKey, sm4Cipher);
//        // 转hex
//        String hexString = Hex.toHexString(sm4Byte);
//        log.info("SM2解密后的SM4明文：{}", hexString);
//
//        // 基于sm4对数据密文进行解密
//        String dataPlainText = sm4DecryptBankData(sm4Byte, dataCipher);
//        log.info("SM4解密后的数据明文：{}", dataPlainText);
//        return dataPlainText;
//
//    }
//
//    private String sm4DecryptBankData(byte[] sm4Byte, String content) {
//        SM4 sm4 = new SM4(Mode.ECB, Padding.PKCS5Padding, sm4Byte);
//        return sm4.decryptStr(content);
//    }
//
//    private byte[] sm2DecryptByte(String privateKey, String cipherText) {
//        Security.addProvider(new BouncyCastleProvider());
//        SM2 sm2 = new SM2(privateKey, null);
//        return sm2.decrypt(cipherText, KeyType.PrivateKey);
//    }
//
//
//    /**
//     * @param sm4Bytes      sm4
//     * @param jsonPlainText 需要加密的内容
//     * @return SM4加密后的数据密文
//     */
//    private String sm4Encrypt(byte[] sm4Bytes, String jsonPlainText) {
//        SM4 sm4 = new SM4(Mode.ECB, Padding.PKCS5Padding, sm4Bytes);
//        String result = sm4.encryptBase64(jsonPlainText, StandardCharsets.UTF_8);
//        log.info("SM4加密后的数据密文：{}", result);
//        // 加密后转base64
//        return result;
//    }
//
//    /**
//     * SM4对称加密
//     *
//     * @param publicKey 公钥
//     * @param sm4Bytes  sm4
//     * @return SM4密钥密文（SM2公钥加密的）
//     */
//    private String sm2EncryptByPublicKey(String publicKey, byte[] sm4Bytes) {
//        Security.addProvider(new BouncyCastleProvider());
//        SM2 sm2 = new SM2(null, publicKey);
//        String result = sm2.encryptHex(sm4Bytes, KeyType.PublicKey);
//        log.info("SM4密钥密文（SM2公钥加密的）：{}", result);
//        return result;
//    }
//
//    /**
//     * 生成随机sm4的secretKey
//     *
//     * @return 生成随机sm4的secretKey
//     */
//    private String randSm4SecretKey() {
//        SM4 sm4 = SmUtil.sm4();
//        SecretKey secretKey = sm4.getSecretKey();
//        byte[] encoded = secretKey.getEncoded();
//        return HexUtil.encodeHexStr(encoded);
//    }
//}