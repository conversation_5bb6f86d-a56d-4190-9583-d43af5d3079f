package com.psbc.xmysfzjjg.service;

import com.psbc.xmysfzjjg.util.ConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;

/**
 * 外联主机服务
 * 整合原有Wlhost类的功能
 */
@Service
public class WlHostService {
    
    private static final Logger logger = LoggerFactory.getLogger(WlHostService.class);
    
    private static final int LEN_SIZE = 6;
    
    @Autowired
    private ConfigService configService;
    
    /**
     * 发送消息到外联主机
     */
    public String sendToWlHost(String message) {
        String ip = configService.getXmysfzjjgValue("IP");
        String portStr = configService.getXmysfzjjgValue("PORT");
        int port = Integer.parseInt(portStr);
        
        logger.info("发送到外联主机: {}:{}", ip, port);
        
        Socket socket = null;
        InputStream is = null;
        OutputStream os = null;
        String response = null;
        
        try {
            socket = new Socket(ip, port);
            is = socket.getInputStream();
            os = socket.getOutputStream();
            
            // 构造请求报文：6位长度 + 消息内容
            String requestMsg = String.format("%06d", message.getBytes().length) + message;
            logger.info("向外联发送请求报文: {}", requestMsg);
            
            // 发送请求
            os.write(requestMsg.getBytes());
            os.flush();
            
            // 读取响应头（6字节长度）
            byte[] retHead = new byte[6];
            logger.info("读取外联系统返回报文头...");
            int n = is.read(retHead);
            
            if (n < 6) {
                String headStr = new String(retHead);
                logger.error("接收外联系统错误：报文头长度不足，实际长度: {}, 内容: {}", n, headStr);
                throw new Exception("接收外联系统报文错误：报文头错误");
            }
            
            String headStr = new String(retHead);
            logger.info("外联系统返回报文头: {}", headStr);
            
            // 解析消息体长度
            int len = Integer.parseInt(headStr.trim());
            
            // 读取消息体
            byte[] msgBody = new byte[len];
            n = is.read(msgBody);
            
            if (n < len) {
                throw new Exception("接收外联系统报文错误：读取长度小于报文头标识长度");
            }
            
            response = new String(msgBody);
            logger.info("外联系统返回消息: {}", response);
            
        } catch (Exception e) {
            logger.error("发送到外联主机时发生异常", e);
            // 返回默认错误响应
            response = configService.getXmysfzjjgValue("MESSAGE");
        } finally {
            // 关闭资源
            closeQuietly(is);
            closeQuietly(os);
            closeQuietly(socket);
        }
        
        return response;
    }
    
    /**
     * 安静地关闭资源
     */
    private void closeQuietly(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                logger.warn("关闭资源时发生异常", e);
            }
        }
    }
    
    /**
     * 安静地关闭Socket
     */
    private void closeQuietly(Socket socket) {
        if (socket != null) {
            try {
                socket.close();
            } catch (IOException e) {
                logger.warn("关闭Socket时发生异常", e);
            }
        }
    }
}
