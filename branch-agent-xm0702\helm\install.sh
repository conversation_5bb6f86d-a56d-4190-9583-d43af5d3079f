#!/bin/bash
# version=0.0.2

function jar_start_command()
{
        command="nohup java -Dapp.name=$1 -Dspring.config.additional-location=classpath:$2/ -Dlogging.config=classpath:$2/logback-spring.xml -Xmx3276M -Xms3276M -XX:+UseParallelGC -XX:+UseParallelOldGC -XX:+PrintGCDetails  -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation  -XX:GCLogFileSize=128M  -XX:NumberOfGCLogFiles=1 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./logs/$1/dumpfile_$1-%t.hprof  -Xloggc:./logs/$1/gc_$1-%t.log -jar $1.jar  >./start_$1.out 2>&1"
        echo "$command"
        $command
}

# 后端部署
# $1 jar name
# $2 env: dev, test, prod
function start_jar()
{
   echo "jar name: $1"
   echo "env: $2"
   mkdir -pv "logs/$1"
   
   jar_start_command $1 $2
}

function main()
{
    #install componet
    # $1 jar name
    # $2 env: dev, test, prod
    if [ "Xbranch-agent" == "X$1" ];then
        start_jar $1 $2
    else
        echo "install error, the $1 is not support."
        exit 1
    fi
}

main $@
exit $?