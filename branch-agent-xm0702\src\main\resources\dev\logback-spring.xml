<?xml version="1.0" encoding="UTF-8"?>
<!-- scan="true"配置文件如果发生变化，将被重新加载，默认为true. -->
<!-- scanPeriod="30 seconds" 自动扫描周期 -->
<!-- debug为true，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。defaultValue="comm-mid" -->
<configuration debug="false" scanPeriod="30 seconds" scan="false">
	<springProperty scope="context" name="APP_NAME" source="spring.application.name"/>
	<!--<property  name="APP_NAME" value="comm-mid"/>-->
	<!-- 设置变量（日志存放位置） -->
	<property  name="LOG_HOME" value="./logs"/>
	<!-- 输出到日志文件 -->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${LOG_HOME}/app-${APP_NAME}-99711940000.log</File>
		<!-- 根据时间来制定滚动策略 %d(表示日期)每天生成一个日志文件-->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${LOG_HOME}/app-${APP_NAME}-99711940000.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<maxHistory>14</maxHistory>
			<totalSizeCap>2GB</totalSizeCap>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>20MB</MaxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<!-- 日志输出格式 -->
		<encoder class="com.psbc.cpufp.common.log.logback.ExPatternLayoutEncoder">
			<pattern>%d{yyyyMMdd-HH:mm:ss.SSS}|%level|%dataCenter|%X{txCode}|%ip:%port|%X{globalBusiTrackNo}|%X{subtxNo}|%processId|%thread|%c.%M.%L|MSG=%msg%n</pattern>
		</encoder>

		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
	</appender>

	<!--  不同业务逻辑的日志，打印到不同文件  -->
	<!--  communication日志  -->
	<appender name="communication_appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${LOG_HOME}/comm-${APP_NAME}-99711940000.log</File>
		<append>true</append>
		<!-- 根据时间来制定滚动策略 %d(表示日期)每天生成一个日志文件-->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${LOG_HOME}/comm-${APP_NAME}-99711940000.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<maxHistory>14</maxHistory>
			<totalSizeCap>2GB</totalSizeCap>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>20MB</MaxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<!-- 日志输出格式 -->
		<encoder class="com.psbc.cpufp.common.log.logback.ExPatternLayoutEncoder">
			<pattern>%d{yyyyMMdd-HH:mm:ss.SSS}|%level|%dataCenter|%X{txCode}|%ip:%port|%X{globalBusiTrackNo}|%X{subtxNo}|%processId|%thread|%c.%M.%L|MSG=%msg%n</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
	</appender>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="com.psbc.cpufp.common.log.logback.ExPatternLayoutEncoder">
			<!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
			<pattern>%d{yyyyMMdd-HH:mm:ss.SSS}|%yellow(%level)|%dataCenter|%X{txCode}|%ip:%port|%X{globalBusiTrackNo}|%X{subtxNo}|%processId|%cyan(%thread)|%boldMagenta(%c.%M.%L)|MSG=%msg%n</pattern>
		</encoder>

		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>info</level>
		</filter>
	</appender>

	<appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>1024</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<appender-ref ref="FILE"/>
		<includeCallerData>false</includeCallerData>
	</appender>

	<appender name="ASYNC_COMM" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>256</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<appender-ref ref="communication_appender"/>
		<includeCallerData>false</includeCallerData>
	</appender>
	<appender name="ASYNC_STDOUT" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>512</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<appender-ref ref="STDOUT"/>
		<includeCallerData>false</includeCallerData>
	</appender>


	<logger name="comm" additivity="true" level="info">
		<appender-ref ref="ASYNC_COMM"/>
	</logger>

	<root level="INFO">
		<appender-ref ref="ASYNC_FILE"/>
		<appender-ref ref="ASYNC_STDOUT"/>
	</root>

</configuration>