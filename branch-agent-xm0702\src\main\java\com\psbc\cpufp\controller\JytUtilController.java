//package com.psbc.cpufp.controller;
//
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.psbc.cpufp.entity.response.RestResponse;
//import com.psbc.cpufp.util.TxCodeConstant;
//import com.psbc.cpufp.util.ZfbhcsUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * 通用接口
// * 公安请求JYT, JTY根据请求跳转
// */
//@RestController
//public class JytUtilController {
//
//    private static final Logger log = LoggerFactory.getLogger(JytUtilController.class);
//
//    @Resource
//    private ZfbhcsUtil zfbhcsUtil;
//
//    /**
//     * @param jsonStr 参数
//     * @return 结果
//     * @throws Exception 抛出异常
//     */
//    @PostMapping("/api/admin/jyt/gaClientService")
//    public RestResponse<String> gaClientService(String jsonStr) throws Exception {
//
//        JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
//        // 判断报文类型编码
//        String txCode = jsonObject.get("TxCode").toString();
//        log.info("报文类型编码：{}", txCode);
//        boolean res = judgeMethod(txCode, jsonObject);
//        if (!res) {
//            return RestResponse.fail("未找到报文类型为" + txCode + "的编码！");
//        }
//        return RestResponse.ok("");
//    }
//
//    /**
//     * @param txCode     报文类型编码
//     * @param jsonObject json对象
//     * @return 结果
//     */
//
//    private boolean judgeMethod(String txCode, JSONObject jsonObject) {
//        log.info("接收到的参数{}", jsonObject);
//        if (TxCodeConstant.ZFBHCS_0101.equals(txCode)) {
//            // TODO 止付保护措施 进入止付的相关处理代码
//            zfbhcsUtil.handleZfbhcs(jsonObject);
//            return true;
//        } else if (TxCodeConstant.ZHXXCX_0305.equals(txCode)) {
//            //<TODO 账户信息查询>
//
//            return true;
//        } else if (TxCodeConstant.ZTXXCX_0303.equals(txCode)) {
//            //<TODO 主体信息查询>
//
//            return true;
//        }
//        return false;
//    }
//
//}
