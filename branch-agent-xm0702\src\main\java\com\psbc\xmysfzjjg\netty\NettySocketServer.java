package com.psbc.xmysfzjjg.netty;

import com.psbc.xmysfzjjg.config.NettyProperties;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.LengthFieldPrepender;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.handler.timeout.IdleStateHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.TimeUnit;

/**
 * Netty Socket服务器
 * 替代原有的PayServerSocket和ServerThread
 */
@Component
public class NettySocketServer {
    
    private static final Logger logger = LoggerFactory.getLogger(NettySocketServer.class);
    
    @Autowired
    private NettyProperties nettyProperties;
    
    @Autowired
    private SocketMessageHandler socketMessageHandler;
    
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private Channel serverChannel;
    
    @PostConstruct
    public void start() {
        try {
            bossGroup = new NioEventLoopGroup(1);
            workerGroup = new NioEventLoopGroup(nettyProperties.getWorkerThreads());
            
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .option(ChannelOption.SO_BACKLOG, 128)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.TCP_NODELAY, true)
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) throws Exception {
                            ChannelPipeline pipeline = ch.pipeline();
                            
                            // 添加空闲状态处理器
                            pipeline.addLast(new IdleStateHandler(
                                    nettyProperties.getConnectTimeout(), 
                                    nettyProperties.getConnectTimeout(), 
                                    nettyProperties.getConnectTimeout(), 
                                    TimeUnit.SECONDS));
                            
                            // 添加长度字段解码器 - 前6字节为长度
                            pipeline.addLast(new LengthFieldBasedFrameDecoder(
                                    Integer.MAX_VALUE, 0, 6, 0, 6));
                            
                            // 添加长度字段编码器
                            pipeline.addLast(new LengthFieldPrepender(6));
                            
                            // 字符串解码器和编码器
                            pipeline.addLast(new StringDecoder());
                            pipeline.addLast(new StringEncoder());
                            
                            // 业务处理器
                            pipeline.addLast(socketMessageHandler);
                        }
                    });
            
            // 绑定端口并启动服务器
            ChannelFuture future = bootstrap.bind(nettyProperties.getSocketPort()).sync();
            serverChannel = future.channel();
            
            logger.info("Netty Socket服务器启动成功，监听端口: {}", nettyProperties.getSocketPort());
            
        } catch (Exception e) {
            logger.error("Netty Socket服务器启动失败", e);
            shutdown();
        }
    }
    
    @PreDestroy
    public void shutdown() {
        try {
            if (serverChannel != null) {
                serverChannel.close().sync();
            }
        } catch (InterruptedException e) {
            logger.error("关闭服务器通道时发生异常", e);
        } finally {
            if (workerGroup != null) {
                workerGroup.shutdownGracefully();
            }
            if (bossGroup != null) {
                bossGroup.shutdownGracefully();
            }
            logger.info("Netty Socket服务器已关闭");
        }
    }
}
