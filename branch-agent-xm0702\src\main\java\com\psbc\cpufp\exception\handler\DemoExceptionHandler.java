//package com.psbc.cpufp.exception.handler;
//
//import com.psbc.cpufp.common.enumcomm.eum.ResponseCodeEnum;
//import com.psbc.cpufp.exception.DemoException;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpStatus;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
///**
// * 自定义异常类DemoException的处理类
// *
// * <AUTHOR>
// */
//@Slf4j
//public class DemoExceptionHandler extends AbstractExceptionHandler {
//    @Override
//    public Object handlerException(Throwable e, HttpServletRequest request, HttpServletResponse response) {
//        // 打印异常信息-打印出的全量异常栈方便面排除代码问题
//        log.error("请求异常：", e);
//        // 该异常默认的异常码和异常描述
//        String respCode = ResponseCodeEnum.REQUEST_PROCESSING_FAILED.getCode();
//        String respDesc = ResponseCodeEnum.REQUEST_PROCESSING_FAILED.getDesc();
//        log.info("响应的异常码：{}和异常描述：{}", respCode, respDesc);
//        // 系统内部异常
//        if (e instanceof DemoException) {
//            // 如果是自定义异常DemoException,从里面取出异常码和异常描述
//            respCode = ((DemoException) e).getCode();
//            respDesc = e.getMessage();
//            // 业务异常返回200
//            response.setStatus(HttpStatus.OK.value());
//        }
//        return super.templateHandler(respCode, respDesc, response);
//    }
//
//
//    @Override
//    public Class<? extends Throwable> getExceptionClass() {
//        return DemoException.class;
//    }
//}
