package com.psbc.xmysfzjjg.config;

import com.psbc.xmysfzjjg.webservice.ClientIWebService;
import com.psbc.xmysfzjjg.webservice.WebServiceImpl;
import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.xml.ws.Endpoint;

/**
 * WebService配置类
 * 配置CXF WebService
 */
@Configuration
public class WebServiceConfig {
    
    @Autowired
    private WebServiceImpl webServiceImpl;
    
    @Autowired
    private WebServiceProperties webServiceProperties;
    
    /**
     * 配置CXF Servlet
     */
    @Bean
    public ServletRegistrationBean<CXFServlet> cxfServlet() {
        return new ServletRegistrationBean<>(new CXFServlet(), "/wservices/*");
    }
    
    /**
     * 配置CXF Bus
     */
    @Bean(name = Bus.DEFAULT_BUS_ID)
    public SpringBus springBus() {
        return new SpringBus();
    }
    
    /**
     * 发布WebService服务端点
     */
    @Bean
    public Endpoint endpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), webServiceImpl);
        endpoint.publish("/IWebServiceService");
        return endpoint;
    }
    
    /**
     * 配置WebService客户端
     */
    @Bean
    public ClientIWebService clientWebService() {
        JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
        factory.setServiceClass(ClientIWebService.class);
        factory.setAddress(webServiceProperties.getClient().getAddress());
        
        // 设置SSL信任所有证书（如果需要）
        // 这里可以添加SSL配置
        
        return (ClientIWebService) factory.create();
    }
}
