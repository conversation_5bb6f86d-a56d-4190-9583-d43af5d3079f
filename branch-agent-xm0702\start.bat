@echo off
echo ========================================
echo Starting Branch Agent with xmysfzjjg
echo ========================================

REM 设置Java环境变量（如果需要）
REM set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_XXX

REM 设置应用参数
set SPRING_PROFILES_ACTIVE=local
set SERVER_PORT=6666

REM 启动应用
echo Starting application...
java -jar target\branch-agent.jar ^
  --spring.profiles.active=%SPRING_PROFILES_ACTIVE% ^
  --server.port=%SERVER_PORT% ^
  --logging.level.com.psbc.xmysfzjjg=DEBUG

echo Application stopped.
pause
