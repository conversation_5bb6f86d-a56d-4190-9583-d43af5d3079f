********-10:06:03.553|INFO|null||***********:null|||38304|background-preinit|org.hibernate.validator.internal.util.Version.?.?|MSG=HV000001: Hibernate Validator 6.2.5.Final
********-10:06:03.644|INFO|null||***********:null|||38304|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Starting BranchAgentApplication using Java 1.8.0_391 on DESKTOP-KE6OBS4 with PID 38304 (C:\workspace\bank\rebuild\branch-agent-xm0702\target\classes started by WLF in C:\workspace\bank\rebuild\branch-agent-xm0702)
********-10:06:03.645|INFO|null||***********:null|||38304|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=The following 1 profile is active: "dev"
********-10:06:04.502|INFO|null||***********:null|||38304|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-10:06:04.702|INFO|null||***********:null|||38304|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-10:06:04.721|INFO|null||***********:null|||38304|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-10:06:11.560|INFO|null||***********:null|||38304|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Multiple Spring Data modules found, entering strict repository configuration mode
********-10:06:11.566|INFO|null||***********:null|||38304|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Bootstrapping Spring Data Redis repositories in DEFAULT mode.
********-10:06:11.629|INFO|null||***********:null|||38304|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
********-10:06:12.066|WARN|null||***********:null|||38304|main|org.mybatis.spring.mapper.ClassPathMapperScanner.?.?|MSG=No MyBatis mapper was found in '[com.psbc.cpufp]' package. Please check your configuration.
********-10:06:12.262|INFO|null||***********:null|||38304|main|org.springframework.cloud.context.scope.GenericScope.?.?|MSG=BeanFactory id=c1547da1-9390-3553-9f8d-24ce7fea1a93
********-10:06:13.029|INFO|null||***********:null|||38304|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat initialized with port(s): 6666 (http)
********-10:06:13.046|INFO|null||***********:null|||38304|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Initializing ProtocolHandler ["http-nio-6666"]
********-10:06:13.047|INFO|null||***********:null|||38304|main|org.apache.catalina.core.StandardService.?.?|MSG=Starting service [Tomcat]
********-10:06:13.047|INFO|null||***********:null|||38304|main|org.apache.catalina.core.StandardEngine.?.?|MSG=Starting Servlet engine: [Apache Tomcat/9.0.73]
********-10:06:13.378|INFO|null||***********:null|||38304|main|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring embedded WebApplicationContext
********-10:06:13.378|INFO|null||***********:null|||38304|main|org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.?.?|MSG=Root WebApplicationContext: initialization completed in 9548 ms
********-10:06:13.997|INFO|null||***********:null|||38304|main|org.springframework.boot.web.servlet.RegistrationBean.?.?|MSG=Servlet CXFServlet was not registered (possibly already registered?)
********-10:06:16.318|INFO|null||***********:6666|||38304|main|com.psbc.xmysfzjjg.netty.NettySocketServer.?.?|MSG=Netty Socket服务器启动成功，监听端口: 8888
********-10:06:16.325|INFO|null||***********:6666|||38304|main|org.soma.job.client.SomaRpcProviderConfig.?.?|MSG=...SOMA>>>>>>>>>>>start
********-10:06:16.327|INFO|null||***********:6666|||38304|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init start after 0s
********-10:06:16.327|INFO|null||***********:6666|||38304|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>SomaProperties(initDelayTime=0, loggerAdapter=, remoteMaxThreadNum=3, remotePort=9944, execVersion=default, testFlag=0, coreAccessToken=, registryUrl=soma-kernel.cpufp-plat:20081, execAccessToken=b79558f7390c45d8bec48aa03fabaaa7, isAutoRegistry=1, execGroupId=EIDbfc5af50-005c-11ee-883c-fa163e7876be, env=default, isOpenDefaultLog=0)
********-10:06:16.829|INFO|null||***********:6666|||38304|main|org.apache.cxf.wsdl.service.factory.ReflectionServiceFactoryBean.?.?|MSG=Creating Service {http://webservice.psbc.com/}IWebServiceService from class com.psbc.xmysfzjjg.webservice.IWebService
********-10:06:17.741|INFO|null||***********:6666|||38304|main|org.apache.cxf.endpoint.ServerImpl.?.?|MSG=Setting the server's publish address to be /IWebServiceService
********-10:06:17.772|INFO|null||***********:6666|||38304|main|org.apache.cxf.wsdl.service.factory.ReflectionServiceFactoryBean.?.?|MSG=Creating Service {http://tempuri.org/}ClientIWebServiceService from class com.psbc.xmysfzjjg.webservice.ClientIWebService
********-10:06:17.842|INFO|null||***********:6666|||38304|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init RpcSpringProviderFactory start-----------------------------
********-10:06:17.848|INFO|null||***********:6666|||38304|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init RpcSpringProviderFactory end-----------------------------
********-10:06:17.849|INFO|null||***********:6666|||38304|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>scan client executor class start
********-10:06:17.853|INFO|null||***********:6666|||38304|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>scan client executor class end
********-10:06:17.855|INFO|null||***********:6666|||38304|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>soma-job use port:9944
********-10:06:19.407|INFO|null||***********:6666|||38304|Thread-35|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=server started, listening on 9944
********-10:06:19.409|INFO|null||***********:6666|||38304|Thread-35|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>soma Auto registry is not use.  config param [isAutoRegistry]=1
********-10:06:21.622|INFO|null||***********:6666|||38304|main|org.springframework.cloud.commons.util.InetUtils.?.?|MSG=Cannot determine local hostname
********-10:06:21.728|INFO|null||***********:6666|||38304|main|org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver.?.?|MSG=Exposing 1 endpoint(s) beneath base path '/actuator'
********-10:06:21.963|INFO|null||***********:6666|||38304|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Starting ProtocolHandler ["http-nio-6666"]
********-10:06:21.994|INFO|null||***********:6666|||38304|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat started on port(s): 6666 (http) with context path ''
********-10:06:23.662|INFO|null||***********:6666|||38304|main|org.springframework.cloud.commons.util.InetUtils.?.?|MSG=Cannot determine local hostname
********-10:06:23.693|INFO|null||***********:6666|||38304|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Started BranchAgentApplication in 26.056 seconds (JVM running for 28.435)
********-10:06:23.705|INFO|null||***********:6666|||38304|main|com.psbc.xmysfzjjg.listener.ApplicationStartupListener.?.?|MSG=应用启动完成，开始初始化xmysfzjjg服务...
********-10:06:23.705|INFO|null||***********:6666|||38304|main|com.psbc.xmysfzjjg.listener.ApplicationStartupListener.?.?|MSG=xmysfzjjg服务初始化完成
********-10:06:24.851|INFO|null||***********:6666|||38304|RMI TCP Connection(8)-**************|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring DispatcherServlet 'dispatcherServlet'
********-10:06:24.851|INFO|null||***********:6666|||38304|RMI TCP Connection(8)-**************|org.springframework.web.servlet.DispatcherServlet.?.?|MSG=Initializing Servlet 'dispatcherServlet'
********-10:06:24.853|INFO|null||***********:6666|||38304|RMI TCP Connection(9)-**************|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Starting...
********-10:06:24.854|INFO|null||***********:6666|||38304|RMI TCP Connection(8)-**************|org.springframework.web.servlet.DispatcherServlet.?.?|MSG=Completed initialization in 3 ms
********-10:06:25.216|INFO|null||***********:6666|||38304|RMI TCP Connection(9)-**************|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Start completed.
********-10:06:47.112|WARN|null||***********:6666|||38304|http-nio-6666-exec-2|org.apache.cxf.phase.PhaseInterceptorChain.?.?|MSG=Interceptor for {http://webservice.psbc.com/}IWebServiceService has thrown exception, unwinding now
org.apache.cxf.interceptor.Fault: Message part {http://service.cpufp.psbc.com/}execute was not recognized.  (Does it exist in service WSDL?)
	at org.apache.cxf.wsdl.interceptors.DocLiteralInInterceptor.validatePart(DocLiteralInInterceptor.java:250)
	at org.apache.cxf.wsdl.interceptors.DocLiteralInInterceptor.handleMessage(DocLiteralInInterceptor.java:189)
	at org.apache.cxf.phase.PhaseInterceptorChain.doIntercept(PhaseInterceptorChain.java:307)
	at org.apache.cxf.transport.ChainInitiationObserver.onMessage(ChainInitiationObserver.java:121)
	at org.apache.cxf.transport.http.AbstractHTTPDestination.invoke(AbstractHTTPDestination.java:265)
	at org.apache.cxf.transport.servlet.ServletController.invokeDestination(ServletController.java:234)
	at org.apache.cxf.transport.servlet.ServletController.invoke(ServletController.java:208)
	at org.apache.cxf.transport.servlet.ServletController.invoke(ServletController.java:160)
	at org.apache.cxf.transport.servlet.CXFNonSpringServlet.invoke(CXFNonSpringServlet.java:225)
	at org.apache.cxf.transport.servlet.AbstractHTTPServlet.handleRequest(AbstractHTTPServlet.java:304)
	at org.apache.cxf.transport.servlet.AbstractHTTPServlet.doPost(AbstractHTTPServlet.java:217)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:528)
	at org.apache.cxf.transport.servlet.AbstractHTTPServlet.service(AbstractHTTPServlet.java:279)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.psbc.cpufp.filter.ResponseFilter.doFilter(ResponseFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
********-10:08:43.865|WARN|null||***********:6666|||38304|http-nio-6666-exec-4|org.apache.cxf.phase.PhaseInterceptorChain.?.?|MSG=Interceptor for {http://webservice.psbc.com/}IWebServiceService#{http://webservice.psbc.com/}Execute has thrown exception, unwinding now
org.apache.cxf.interceptor.Fault: Unexpected wrapper element {http://service.cpufp.psbc.com/}Execute found.   Expected {http://webservice.psbc.com/}Execute.
	at org.apache.cxf.wsdl.interceptors.DocLiteralInInterceptor.handleMessage(DocLiteralInInterceptor.java:107)
	at org.apache.cxf.phase.PhaseInterceptorChain.doIntercept(PhaseInterceptorChain.java:307)
	at org.apache.cxf.transport.ChainInitiationObserver.onMessage(ChainInitiationObserver.java:121)
	at org.apache.cxf.transport.http.AbstractHTTPDestination.invoke(AbstractHTTPDestination.java:265)
	at org.apache.cxf.transport.servlet.ServletController.invokeDestination(ServletController.java:234)
	at org.apache.cxf.transport.servlet.ServletController.invoke(ServletController.java:208)
	at org.apache.cxf.transport.servlet.ServletController.invoke(ServletController.java:160)
	at org.apache.cxf.transport.servlet.CXFNonSpringServlet.invoke(CXFNonSpringServlet.java:225)
	at org.apache.cxf.transport.servlet.AbstractHTTPServlet.handleRequest(AbstractHTTPServlet.java:304)
	at org.apache.cxf.transport.servlet.AbstractHTTPServlet.doPost(AbstractHTTPServlet.java:217)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:528)
	at org.apache.cxf.transport.servlet.AbstractHTTPServlet.service(AbstractHTTPServlet.java:279)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.psbc.cpufp.filter.ResponseFilter.doFilter(ResponseFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
********-10:10:10.348|WARN|null||***********:6666|||38304|http-nio-6666-exec-8|org.apache.cxf.phase.PhaseInterceptorChain.?.?|MSG=Interceptor for {http://webservice.psbc.com/}IWebServiceService has thrown exception, unwinding now
org.apache.cxf.binding.soap.SoapFault: Error reading XMLStreamReader: Undeclared namespace prefix "tns"
 at [row,col {unknown-source}]: [4,19]
	at org.apache.cxf.binding.soap.interceptor.CheckFaultInterceptor.handleMessage(CheckFaultInterceptor.java:64)
	at org.apache.cxf.binding.soap.interceptor.CheckFaultInterceptor.handleMessage(CheckFaultInterceptor.java:35)
	at org.apache.cxf.phase.PhaseInterceptorChain.doIntercept(PhaseInterceptorChain.java:307)
	at org.apache.cxf.transport.ChainInitiationObserver.onMessage(ChainInitiationObserver.java:121)
	at org.apache.cxf.transport.http.AbstractHTTPDestination.invoke(AbstractHTTPDestination.java:265)
	at org.apache.cxf.transport.servlet.ServletController.invokeDestination(ServletController.java:234)
	at org.apache.cxf.transport.servlet.ServletController.invoke(ServletController.java:208)
	at org.apache.cxf.transport.servlet.ServletController.invoke(ServletController.java:160)
	at org.apache.cxf.transport.servlet.CXFNonSpringServlet.invoke(CXFNonSpringServlet.java:225)
	at org.apache.cxf.transport.servlet.AbstractHTTPServlet.handleRequest(AbstractHTTPServlet.java:304)
	at org.apache.cxf.transport.servlet.AbstractHTTPServlet.doPost(AbstractHTTPServlet.java:217)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:528)
	at org.apache.cxf.transport.servlet.AbstractHTTPServlet.service(AbstractHTTPServlet.java:279)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.psbc.cpufp.filter.ResponseFilter.doFilter(ResponseFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.ctc.wstx.exc.WstxParsingException: Undeclared namespace prefix "tns"
 at [row,col {unknown-source}]: [4,19]
	at com.ctc.wstx.sr.StreamScanner.constructWfcException(StreamScanner.java:634)
	at com.ctc.wstx.sr.StreamScanner.throwParseError(StreamScanner.java:504)
	at com.ctc.wstx.sr.InputElementStack.resolveAndValidateElement(InputElementStack.java:503)
	at com.ctc.wstx.sr.BasicStreamReader.handleStartElem(BasicStreamReader.java:3066)
	at com.ctc.wstx.sr.BasicStreamReader.nextFromTree(BasicStreamReader.java:2928)
	at com.ctc.wstx.sr.BasicStreamReader.next(BasicStreamReader.java:1122)
	at org.apache.cxf.binding.soap.interceptor.CheckFaultInterceptor.handleMessage(CheckFaultInterceptor.java:56)
	... 52 common frames omitted
********-10:11:50.833|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=>>>>>>>>>>>>>>>>>>>>>>>>>>接收委托方webservice报文开始>>>>>>>>>>>>>>>>>>>>>>>>>
********-10:11:50.833|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=BankID: 20001, 报文inParameter: <?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20001</serviceno><usr>xmjydj</usr><pwd></pwd><optname></optname><signmsg></signmsg></head><body><accountname>XXX</accountname><accountno>XXX</accountno><bankid>XXX</bankid></body></content>
********-10:11:50.833|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=报文发往外联系统开始>>>
********-10:11:50.834|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=发送到外联主机: 127.0.0.1:9702
********-10:11:50.837|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=向外联发送请求报文: 000253<?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20001</serviceno><usr>xmjydj</usr><pwd></pwd><optname></optname><signmsg></signmsg></head><body><accountname>XXX</accountname><accountno>XXX</accountno><bankid>XXX</bankid></body></content>
********-10:11:50.838|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=读取外联系统返回报文头...
********-10:11:51.167|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=外联系统返回报文头: 000480
********-10:11:51.167|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=外联系统返回消息: <?xml version="1.0" encoding="utf-8"?>
<content>
  <head>
    <statecode>1</statecode>
    <msg>交易成功</msg>
    <timestamp>*************</timestamp>
  </head>
  <body>
    <table_account>
      <row>
        <instructionno>MOCK*************</instructionno>
        <issuccess>1</issuccess>
        <amount>100000.00</amount>
        <balance>999999.99</balance>
        <transactionTime>*************</transactionTime>
      </row>
    </table_account>
  </body>
</content>
********-10:11:51.167|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=报文发往外联系统结束>>>
********-10:11:51.167|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=外联系统返回报文耗时: 333 毫秒
********-10:11:51.167|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=外联系统返回报文长度: 472
********-10:11:51.167|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=外联系统返回报文: [<?xml version="1.0" encoding="utf-8"?>
<content>
  <head>
    <statecode>1</statecode>
    <msg>交易成功</msg>
    <timestamp>*************</timestamp>
  </head>
  <body>
    <table_account>
      <row>
        <instructionno>MOCK*************</instructionno>
        <issuccess>1</issuccess>
        <amount>100000.00</amount>
        <balance>999999.99</balance>
        <transactionTime>*************</transactionTime>
      </row>
    </table_account>
  </body>
</content>]
********-10:11:51.167|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=>>>>>>>>>>>>>>>>>>>>>>>>>>委托方webservice报文处理结束>>>>>>>>>>>>>>>>>>>>>>>>>
********-10:12:52.870|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=>>>>>>>>>>>>>>>>>>>>>>>>>>接收委托方webservice报文开始>>>>>>>>>>>>>>>>>>>>>>>>>
********-10:12:52.870|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=BankID: 20001, 报文inParameter: <?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20001</serviceno><usr>xmjydj</usr><pwd></pwd><optname></optname><signmsg></signmsg></head><body><accountname>XXX</accountname><accountno>XXX</accountno><bankid>XXX</bankid></body></content>
********-10:12:52.870|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=报文发往外联系统开始>>>
********-10:12:52.870|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=发送到外联主机: 127.0.0.1:9702
********-10:12:52.872|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=向外联发送请求报文: 000253<?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20001</serviceno><usr>xmjydj</usr><pwd></pwd><optname></optname><signmsg></signmsg></head><body><accountname>XXX</accountname><accountno>XXX</accountno><bankid>XXX</bankid></body></content>
********-10:12:52.872|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=读取外联系统返回报文头...
********-10:12:52.946|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=外联系统返回报文头: 000480
********-10:12:52.946|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=外联系统返回消息: <?xml version="1.0" encoding="utf-8"?>
<content>
  <head>
    <statecode>1</statecode>
    <msg>交易成功</msg>
    <timestamp>*************</timestamp>
  </head>
  <body>
    <table_account>
      <row>
        <instructionno>MOCK*************</instructionno>
        <issuccess>1</issuccess>
        <amount>100000.00</amount>
        <balance>999999.99</balance>
        <transactionTime>*************</transactionTime>
      </row>
    </table_account>
  </body>
</content>
********-10:12:52.946|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=报文发往外联系统结束>>>
********-10:12:52.946|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=外联系统返回报文耗时: 76 毫秒
********-10:12:52.946|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=外联系统返回报文长度: 472
********-10:12:52.946|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=外联系统返回报文: [<?xml version="1.0" encoding="utf-8"?>
<content>
  <head>
    <statecode>1</statecode>
    <msg>交易成功</msg>
    <timestamp>*************</timestamp>
  </head>
  <body>
    <table_account>
      <row>
        <instructionno>MOCK*************</instructionno>
        <issuccess>1</issuccess>
        <amount>100000.00</amount>
        <balance>999999.99</balance>
        <transactionTime>*************</transactionTime>
      </row>
    </table_account>
  </body>
</content>]
********-10:12:52.946|INFO|null||***********:6666|||38304|http-nio-6666-exec-4|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=>>>>>>>>>>>>>>>>>>>>>>>>>>委托方webservice报文处理结束>>>>>>>>>>>>>>>>>>>>>>>>>
********-10:13:13.915|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=>>>>>>>>>>>>>>>>>>>>>>>>>>接收委托方webservice报文开始>>>>>>>>>>>>>>>>>>>>>>>>>
********-10:13:13.915|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=BankID: 20001, 报文inParameter: <?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20001</serviceno><usr>xmjydj</usr><pwd></pwd><optname></optname><signmsg></signmsg></head><body><accountname>XXX</accountname><accountno>XXX</accountno><bankid>XXX</bankid></body></content>
********-10:13:13.915|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=报文发往外联系统开始>>>
********-10:13:13.915|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=发送到外联主机: 127.0.0.1:9702
********-10:13:13.916|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=向外联发送请求报文: 000253<?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20001</serviceno><usr>xmjydj</usr><pwd></pwd><optname></optname><signmsg></signmsg></head><body><accountname>XXX</accountname><accountno>XXX</accountno><bankid>XXX</bankid></body></content>
********-10:13:13.917|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=读取外联系统返回报文头...
********-10:13:13.979|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=外联系统返回报文头: 000480
********-10:13:13.979|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.service.WlHostService.?.?|MSG=外联系统返回消息: <?xml version="1.0" encoding="utf-8"?>
<content>
  <head>
    <statecode>1</statecode>
    <msg>交易成功</msg>
    <timestamp>*************</timestamp>
  </head>
  <body>
    <table_account>
      <row>
        <instructionno>MOCK*************</instructionno>
        <issuccess>1</issuccess>
        <amount>100000.00</amount>
        <balance>999999.99</balance>
        <transactionTime>*************</transactionTime>
      </row>
    </table_account>
  </body>
</content>
********-10:13:13.979|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=报文发往外联系统结束>>>
********-10:13:13.979|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=外联系统返回报文耗时: 64 毫秒
********-10:13:13.979|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=外联系统返回报文长度: 472
********-10:13:13.979|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=外联系统返回报文: [<?xml version="1.0" encoding="utf-8"?>
<content>
  <head>
    <statecode>1</statecode>
    <msg>交易成功</msg>
    <timestamp>*************</timestamp>
  </head>
  <body>
    <table_account>
      <row>
        <instructionno>MOCK*************</instructionno>
        <issuccess>1</issuccess>
        <amount>100000.00</amount>
        <balance>999999.99</balance>
        <transactionTime>*************</transactionTime>
      </row>
    </table_account>
  </body>
</content>]
********-10:13:13.979|INFO|null||***********:6666|||38304|http-nio-6666-exec-10|com.psbc.xmysfzjjg.webservice.WebServiceImpl.?.?|MSG=>>>>>>>>>>>>>>>>>>>>>>>>>>委托方webservice报文处理结束>>>>>>>>>>>>>>>>>>>>>>>>>
********-10:16:24.441|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.netty.SocketMessageHandler.?.?|MSG=新的客户端连接: /127.0.0.1:57398
********-10:16:24.459|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.netty.SocketMessageHandler.?.?|MSG=接收到消息: <?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20006</serviceno><usr>xmjydj</usr><pwd></pwd><optname></optname><signmsg></signmsg></head><body><serialno>XXX</serialno><accountname>XXX</accountname><accountno>XXX</accountno><executetype>XXX</executetype><executeamount>XXX</executeamount><executedept>XXX</executedept><executedate>XXX</executedate><releaseserialno>XXX</releaseserialno><releasetime>XXX</releasetime><note>XXX</note></body></content>
********-10:16:24.460|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.service.MessageProcessService.?.?|MSG=开始处理消息: <?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20006</serviceno><usr>xmjydj</usr><pwd></pwd><optname></optname><signmsg></signmsg></head><body><serialno>XXX</serialno><accountname>XXX</accountname><accountno>XXX</accountno><executetype>XXX</executetype><executeamount>XXX</executeamount><executedept>XXX</executedept><executedate>XXX</executedate><releaseserialno>XXX</releaseserialno><releasetime>XXX</releasetime><note>XXX</note></body></content>
********-10:16:24.462|WARN|null||***********:6666|||38304|nioEventLoopGroup-5-1|io.netty.channel.ChannelInitializer.?.?|MSG=Failed to initialize a channel. Closing: [id: 0x5c3f9150, L:/127.0.0.1:8888 - R:/127.0.0.1:57397]
io.netty.channel.ChannelPipelineException: com.psbc.xmysfzjjg.netty.SocketMessageHandler is not a @Sharable handler, so can't be added or removed multiple times.
	at io.netty.channel.DefaultChannelPipeline.checkMultiplicity(DefaultChannelPipeline.java:600)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:202)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:381)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:370)
	at com.psbc.xmysfzjjg.netty.NettySocketServer$1.initChannel(NettySocketServer.java:75)
	at com.psbc.xmysfzjjg.netty.NettySocketServer$1.initChannel(NettySocketServer.java:54)
	at io.netty.channel.ChannelInitializer.initChannel(ChannelInitializer.java:129)
	at io.netty.channel.ChannelInitializer.handlerAdded(ChannelInitializer.java:112)
	at io.netty.channel.AbstractChannelHandlerContext.callHandlerAdded(AbstractChannelHandlerContext.java:1114)
	at io.netty.channel.DefaultChannelPipeline.callHandlerAdded0(DefaultChannelPipeline.java:609)
	at io.netty.channel.DefaultChannelPipeline.access$100(DefaultChannelPipeline.java:46)
	at io.netty.channel.DefaultChannelPipeline$PendingHandlerAddedTask.execute(DefaultChannelPipeline.java:1463)
	at io.netty.channel.DefaultChannelPipeline.callHandlerAddedForAllHandlers(DefaultChannelPipeline.java:1115)
	at io.netty.channel.DefaultChannelPipeline.invokeHandlerAddedIfNeeded(DefaultChannelPipeline.java:650)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:514)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:429)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:486)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
********-10:16:24.701|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.service.MessageProcessService.?.?|MSG=检测到XML格式消息
********-10:16:24.704|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.service.MessageProcessService.?.?|MSG=XML根节点名称: content
********-10:16:24.705|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.service.MessageProcessService.?.?|MSG=从XML中提取的服务号: 20006
********-10:16:24.705|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.service.MessageProcessService.?.?|MSG=提取的服务号: 20006
********-10:16:24.705|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.service.MessageProcessService.?.?|MSG=调用综合办公20006接口，查询监管账户变动反馈信息
********-10:16:24.705|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.service.ZhbgService.?.?|MSG=开始请求20006接口，URL: http://127.0.0.1:9090/api/admin/xzp/queryJgzhInfo
********-10:16:24.707|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.service.ZhbgService.?.?|MSG=传入XML参数: <?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20006</serviceno><usr>xmjydj</usr><pwd></pwd><optname></optname><signmsg></signmsg></head><body><serialno>XXX</serialno><accountname>XXX</accountname><accountno>XXX</accountno><executetype>XXX</executetype><executeamount>XXX</executeamount><executedept>XXX</executedept><executedate>XXX</executedate><releaseserialno>XXX</releaseserialno><releasetime>XXX</releasetime><note>XXX</note></body></content>
********-10:16:24.713|ERROR|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.service.ZhbgService.?.?|MSG=发送XML请求异常
java.lang.ClassCastException: sun.net.www.protocol.http.HttpURLConnection cannot be cast to javax.net.ssl.HttpsURLConnection
	at com.psbc.xmysfzjjg.service.ZhbgService.sendXmlRequest(ZhbgService.java:87)
	at com.psbc.xmysfzjjg.service.ZhbgService.sendZhbgRequest(ZhbgService.java:51)
	at com.psbc.xmysfzjjg.service.MessageProcessService.processMessage(MessageProcessService.java:55)
	at com.psbc.xmysfzjjg.netty.SocketMessageHandler.channelRead0(SocketMessageHandler.java:46)
	at com.psbc.xmysfzjjg.netty.SocketMessageHandler.channelRead0(SocketMessageHandler.java:20)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.timeout.IdleStateHandler.channelRead(IdleStateHandler.java:286)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
********-10:16:24.714|ERROR|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.netty.SocketMessageHandler.?.?|MSG=处理消息时发生异常
java.lang.ClassCastException: sun.net.www.protocol.http.HttpURLConnection cannot be cast to javax.net.ssl.HttpsURLConnection
	at com.psbc.xmysfzjjg.service.ZhbgService.sendXmlRequest(ZhbgService.java:87)
	at com.psbc.xmysfzjjg.service.ZhbgService.sendZhbgRequest(ZhbgService.java:51)
	at com.psbc.xmysfzjjg.service.MessageProcessService.processMessage(MessageProcessService.java:55)
	at com.psbc.xmysfzjjg.netty.SocketMessageHandler.channelRead0(SocketMessageHandler.java:46)
	at com.psbc.xmysfzjjg.netty.SocketMessageHandler.channelRead0(SocketMessageHandler.java:20)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.timeout.IdleStateHandler.channelRead(IdleStateHandler.java:286)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
********-10:16:24.740|INFO|null||***********:6666|||38304|nioEventLoopGroup-5-2|com.psbc.xmysfzjjg.netty.SocketMessageHandler.?.?|MSG=客户端断开连接: /127.0.0.1:57398
********-11:05:55.643|WARN|null||***********:6666|||38304|nioEventLoopGroup-5-3|io.netty.channel.ChannelInitializer.?.?|MSG=Failed to initialize a channel. Closing: [id: 0xadff656c, L:/127.0.0.1:8888 - R:/127.0.0.1:59168]
io.netty.channel.ChannelPipelineException: com.psbc.xmysfzjjg.netty.SocketMessageHandler is not a @Sharable handler, so can't be added or removed multiple times.
	at io.netty.channel.DefaultChannelPipeline.checkMultiplicity(DefaultChannelPipeline.java:600)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:202)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:381)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:370)
	at com.psbc.xmysfzjjg.netty.NettySocketServer$1.initChannel(NettySocketServer.java:75)
	at com.psbc.xmysfzjjg.netty.NettySocketServer$1.initChannel(NettySocketServer.java:54)
	at io.netty.channel.ChannelInitializer.initChannel(ChannelInitializer.java:129)
	at io.netty.channel.ChannelInitializer.handlerAdded(ChannelInitializer.java:112)
	at io.netty.channel.AbstractChannelHandlerContext.callHandlerAdded(AbstractChannelHandlerContext.java:1114)
	at io.netty.channel.DefaultChannelPipeline.callHandlerAdded0(DefaultChannelPipeline.java:609)
	at io.netty.channel.DefaultChannelPipeline.access$100(DefaultChannelPipeline.java:46)
	at io.netty.channel.DefaultChannelPipeline$PendingHandlerAddedTask.execute(DefaultChannelPipeline.java:1463)
	at io.netty.channel.DefaultChannelPipeline.callHandlerAddedForAllHandlers(DefaultChannelPipeline.java:1115)
	at io.netty.channel.DefaultChannelPipeline.invokeHandlerAddedIfNeeded(DefaultChannelPipeline.java:650)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:514)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:429)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:486)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
********-11:07:55.274|INFO|null||***********:6666|||38304|http-nio-6666-exec-7|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--preHandle--putMDC
********-11:07:55.399|INFO|null||***********:6666|||38304|http-nio-6666-exec-7|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--afterCompletion--clearMDC
********-11:07:55.549|INFO|null||***********:6666|||38304|http-nio-6666-exec-1|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--preHandle--putMDC
********-11:07:55.554|INFO|null||***********:6666|||38304|http-nio-6666-exec-1|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--afterCompletion--clearMDC
********-11:07:55.561|INFO|null||***********:6666|||38304|http-nio-6666-exec-2|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--preHandle--putMDC
********-11:07:55.564|INFO|null||***********:6666|||38304|http-nio-6666-exec-2|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--afterCompletion--clearMDC
********-11:07:55.577|INFO|null||***********:6666|||38304|http-nio-6666-exec-5|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--preHandle--putMDC
********-11:07:55.611|INFO|null||***********:6666|||38304|http-nio-6666-exec-5|com.psbc.xmysfzjjg.controller.ZhbgController.?.?|MSG=--------------------进入请求综合办公获取按揭贷款信息接口------------------------------------------
********-11:07:55.612|INFO|null||***********:6666|||38304|http-nio-6666-exec-5|com.psbc.xmysfzjjg.controller.ZhbgController.?.?|MSG=请求综合办公参数: {"serviceno":"30001","data":"test request"}
********-11:07:55.612|INFO|null||***********:6666|||38304|http-nio-6666-exec-5|com.psbc.xmysfzjjg.service.ZhbgService.?.?|MSG=开始请求30001接口，URL: http://127.0.0.1:9090/api/admin/xzp/queryYxjfLsxdye
********-11:07:55.612|INFO|null||***********:6666|||38304|http-nio-6666-exec-5|com.psbc.xmysfzjjg.service.ZhbgService.?.?|MSG=传入JSON参数: {"serviceno":"30001","data":"test request"}
********-11:07:57.735|ERROR|null||***********:6666|||38304|http-nio-6666-exec-5|com.psbc.xmysfzjjg.service.ZhbgService.?.?|MSG=发送JSON请求异常
cn.hutool.core.io.IORuntimeException: ConnectException: Connection refused: connect
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1328)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1176)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1050)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1026)
	at com.psbc.xmysfzjjg.service.ZhbgService.sendJsonRequest(ZhbgService.java:66)
	at com.psbc.xmysfzjjg.service.ZhbgService.sendZhbgRequest(ZhbgService.java:44)
	at com.psbc.xmysfzjjg.controller.ZhbgController.queryYxjfLsxdye(ZhbgController.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:528)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.psbc.cpufp.filter.ResponseFilter.doFilter(ResponseFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:499)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:594)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:278)
	at sun.net.www.http.HttpClient.New(HttpClient.java:375)
	at sun.net.www.http.HttpClient.New(HttpClient.java:393)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1354)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1329)
	at cn.hutool.http.HttpConnection.getOutputStream(HttpConnection.java:458)
	at cn.hutool.http.HttpRequest.sendFormUrlEncoded(HttpRequest.java:1351)
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1320)
	... 63 common frames omitted
********-11:07:57.736|ERROR|null||***********:6666|||38304|http-nio-6666-exec-5|com.psbc.xmysfzjjg.controller.ZhbgController.?.?|MSG=处理按揭贷款信息查询请求时发生异常
cn.hutool.core.io.IORuntimeException: ConnectException: Connection refused: connect
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1328)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1176)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1050)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1026)
	at com.psbc.xmysfzjjg.service.ZhbgService.sendJsonRequest(ZhbgService.java:66)
	at com.psbc.xmysfzjjg.service.ZhbgService.sendZhbgRequest(ZhbgService.java:44)
	at com.psbc.xmysfzjjg.controller.ZhbgController.queryYxjfLsxdye(ZhbgController.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:528)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.psbc.cpufp.filter.ResponseFilter.doFilter(ResponseFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:499)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:594)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:278)
	at sun.net.www.http.HttpClient.New(HttpClient.java:375)
	at sun.net.www.http.HttpClient.New(HttpClient.java:393)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1354)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1329)
	at cn.hutool.http.HttpConnection.getOutputStream(HttpConnection.java:458)
	at cn.hutool.http.HttpRequest.sendFormUrlEncoded(HttpRequest.java:1351)
	at cn.hutool.http.HttpRequest.send(HttpRequest.java:1320)
	... 63 common frames omitted
********-11:07:57.740|INFO|null||***********:6666|||38304|http-nio-6666-exec-5|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--afterCompletion--clearMDC
********-11:07:57.751|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--preHandle--putMDC
********-11:07:57.753|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.controller.ZhbgController.?.?|MSG=--------------------进入请求综合办公查询监管账户信息接口------------------------------------------
********-11:07:57.753|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.controller.ZhbgController.?.?|MSG=请求综合办公参数: <?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20006</serviceno></head><body><data>test request</data></body></content>
********-11:07:57.753|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.service.ZhbgService.?.?|MSG=开始请求20006接口，URL: http://127.0.0.1:9090/api/admin/xzp/queryJgzhInfo
********-11:07:57.753|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.service.ZhbgService.?.?|MSG=传入XML参数: <?xml version="1.0" encoding="utf-8"?><content><head><serviceno>20006</serviceno></head><body><data>test request</data></body></content>
********-11:07:57.754|ERROR|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.service.ZhbgService.?.?|MSG=发送XML请求异常
java.lang.ClassCastException: sun.net.www.protocol.http.HttpURLConnection cannot be cast to javax.net.ssl.HttpsURLConnection
	at com.psbc.xmysfzjjg.service.ZhbgService.sendXmlRequest(ZhbgService.java:87)
	at com.psbc.xmysfzjjg.service.ZhbgService.sendZhbgRequest(ZhbgService.java:51)
	at com.psbc.xmysfzjjg.controller.ZhbgController.queryJgzhInfo(ZhbgController.java:50)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:528)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.psbc.cpufp.filter.ResponseFilter.doFilter(ResponseFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
********-11:07:57.754|ERROR|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.xmysfzjjg.controller.ZhbgController.?.?|MSG=处理监管账户信息查询请求时发生异常
java.lang.ClassCastException: sun.net.www.protocol.http.HttpURLConnection cannot be cast to javax.net.ssl.HttpsURLConnection
	at com.psbc.xmysfzjjg.service.ZhbgService.sendXmlRequest(ZhbgService.java:87)
	at com.psbc.xmysfzjjg.service.ZhbgService.sendZhbgRequest(ZhbgService.java:51)
	at com.psbc.xmysfzjjg.controller.ZhbgController.queryJgzhInfo(ZhbgController.java:50)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:528)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.psbc.cpufp.filter.ResponseFilter.doFilter(ResponseFilter.java:42)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
********-11:07:57.756|INFO|null||***********:6666|||38304|http-nio-6666-exec-6|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--afterCompletion--clearMDC
********-11:07:57.762|INFO|null||***********:6666|||38304|http-nio-6666-exec-3|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--preHandle--putMDC
********-11:07:57.765|INFO|null||***********:6666|||38304|http-nio-6666-exec-3|com.psbc.cpufp.common.log.intercepter.LogInterceptor.?.?|MSG=LogInterceptor--afterCompletion--clearMDC
********-11:20:53.135|WARN|null||***********:6666|||38304|nioEventLoopGroup-5-4|io.netty.channel.ChannelInitializer.?.?|MSG=Failed to initialize a channel. Closing: [id: 0x4ed050e2, L:/127.0.0.1:8888 - R:/127.0.0.1:59686]
io.netty.channel.ChannelPipelineException: com.psbc.xmysfzjjg.netty.SocketMessageHandler is not a @Sharable handler, so can't be added or removed multiple times.
	at io.netty.channel.DefaultChannelPipeline.checkMultiplicity(DefaultChannelPipeline.java:600)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:202)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:381)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:370)
	at com.psbc.xmysfzjjg.netty.NettySocketServer$1.initChannel(NettySocketServer.java:75)
	at com.psbc.xmysfzjjg.netty.NettySocketServer$1.initChannel(NettySocketServer.java:54)
	at io.netty.channel.ChannelInitializer.initChannel(ChannelInitializer.java:129)
	at io.netty.channel.ChannelInitializer.handlerAdded(ChannelInitializer.java:112)
	at io.netty.channel.AbstractChannelHandlerContext.callHandlerAdded(AbstractChannelHandlerContext.java:1114)
	at io.netty.channel.DefaultChannelPipeline.callHandlerAdded0(DefaultChannelPipeline.java:609)
	at io.netty.channel.DefaultChannelPipeline.access$100(DefaultChannelPipeline.java:46)
	at io.netty.channel.DefaultChannelPipeline$PendingHandlerAddedTask.execute(DefaultChannelPipeline.java:1463)
	at io.netty.channel.DefaultChannelPipeline.callHandlerAddedForAllHandlers(DefaultChannelPipeline.java:1115)
	at io.netty.channel.DefaultChannelPipeline.invokeHandlerAddedIfNeeded(DefaultChannelPipeline.java:650)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:514)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:429)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:486)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
********-11:35:50.433|WARN|null||***********:6666|||38304|nioEventLoopGroup-5-5|io.netty.channel.ChannelInitializer.?.?|MSG=Failed to initialize a channel. Closing: [id: 0xdee8a5cd, L:/127.0.0.1:8888 - R:/127.0.0.1:60213]
io.netty.channel.ChannelPipelineException: com.psbc.xmysfzjjg.netty.SocketMessageHandler is not a @Sharable handler, so can't be added or removed multiple times.
	at io.netty.channel.DefaultChannelPipeline.checkMultiplicity(DefaultChannelPipeline.java:600)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:202)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:381)
	at io.netty.channel.DefaultChannelPipeline.addLast(DefaultChannelPipeline.java:370)
	at com.psbc.xmysfzjjg.netty.NettySocketServer$1.initChannel(NettySocketServer.java:75)
	at com.psbc.xmysfzjjg.netty.NettySocketServer$1.initChannel(NettySocketServer.java:54)
	at io.netty.channel.ChannelInitializer.initChannel(ChannelInitializer.java:129)
	at io.netty.channel.ChannelInitializer.handlerAdded(ChannelInitializer.java:112)
	at io.netty.channel.AbstractChannelHandlerContext.callHandlerAdded(AbstractChannelHandlerContext.java:1114)
	at io.netty.channel.DefaultChannelPipeline.callHandlerAdded0(DefaultChannelPipeline.java:609)
	at io.netty.channel.DefaultChannelPipeline.access$100(DefaultChannelPipeline.java:46)
	at io.netty.channel.DefaultChannelPipeline$PendingHandlerAddedTask.execute(DefaultChannelPipeline.java:1463)
	at io.netty.channel.DefaultChannelPipeline.callHandlerAddedForAllHandlers(DefaultChannelPipeline.java:1115)
	at io.netty.channel.DefaultChannelPipeline.invokeHandlerAddedIfNeeded(DefaultChannelPipeline.java:650)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:514)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:429)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:486)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
