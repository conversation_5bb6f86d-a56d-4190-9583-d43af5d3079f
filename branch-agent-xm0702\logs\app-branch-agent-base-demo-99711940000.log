********-11:04:03.245|INFO|null||***********:null|||32488|background-preinit|org.hibernate.validator.internal.util.Version.?.?|MSG=HV000001: Hibernate Validator 6.2.5.Final
********-11:04:03.278|INFO|null||***********:null|||32488|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Starting BranchAgentApplication using Java 1.8.0_391 on DESKTOP-KE6OBS4 with PID 32488 (C:\workspace\bank\branch-agent-xm0417\target\classes started by WLF in C:\workspace\bank\branch-agent-xm0417)
********-11:04:03.294|INFO|null||***********:null|||32488|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=The following 1 profile is active: "dev"
********-11:04:03.936|INFO|null||***********:null|||32488|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-11:04:04.024|INFO|null||***********:null|||32488|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-11:04:04.033|INFO|null||***********:null|||32488|main|com.psbc.cpufp.common.cache.condition.RedisCondition.?.?|MSG=matches result,is hfCluster = false,is lfCluster = false
********-11:04:08.785|INFO|null||***********:null|||32488|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Multiple Spring Data modules found, entering strict repository configuration mode
********-11:04:08.794|INFO|null||***********:null|||32488|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Bootstrapping Spring Data Redis repositories in DEFAULT mode.
********-11:04:08.876|INFO|null||***********:null|||32488|main|org.springframework.data.repository.config.RepositoryConfigurationDelegate.?.?|MSG=Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
********-11:04:09.178|WARN|null||***********:null|||32488|main|org.mybatis.spring.mapper.ClassPathMapperScanner.?.?|MSG=No MyBatis mapper was found in '[com.psbc.cpufp]' package. Please check your configuration.
********-11:04:09.305|INFO|null||***********:null|||32488|main|org.springframework.cloud.context.scope.GenericScope.?.?|MSG=BeanFactory id=ac2e8b06-c851-3e4c-8800-a67f59337d01
********-11:04:10.189|INFO|null||***********:null|||32488|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat initialized with port(s): 6666 (http)
********-11:04:10.213|INFO|null||***********:null|||32488|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Initializing ProtocolHandler ["http-nio-6666"]
********-11:04:10.215|INFO|null||***********:null|||32488|main|org.apache.catalina.core.StandardService.?.?|MSG=Starting service [Tomcat]
********-11:04:10.215|INFO|null||***********:null|||32488|main|org.apache.catalina.core.StandardEngine.?.?|MSG=Starting Servlet engine: [Apache Tomcat/9.0.73]
********-11:04:10.589|INFO|null||***********:null|||32488|main|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring embedded WebApplicationContext
********-11:04:10.590|INFO|null||***********:null|||32488|main|org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.?.?|MSG=Root WebApplicationContext: initialization completed in 7179 ms
********-11:04:12.832|INFO|null||***********:6666|||32488|main|org.soma.job.client.SomaRpcProviderConfig.?.?|MSG=...SOMA>>>>>>>>>>>start
********-11:04:12.834|INFO|null||***********:6666|||32488|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init start after 0s
********-11:04:12.834|INFO|null||***********:6666|||32488|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>SomaProperties(initDelayTime=0, loggerAdapter=, remoteMaxThreadNum=3, remotePort=9944, execVersion=default, testFlag=0, coreAccessToken=, registryUrl=soma-kernel.cpufp-plat:20081, execAccessToken=b79558f7390c45d8bec48aa03fabaaa7, isAutoRegistry=1, execGroupId=EIDbfc5af50-005c-11ee-883c-fa163e7876be, env=default, isOpenDefaultLog=0)
********-11:04:12.976|INFO|null||***********:6666|||32488|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init RpcSpringProviderFactory start-----------------------------
********-11:04:12.981|INFO|null||***********:6666|||32488|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>init RpcSpringProviderFactory end-----------------------------
********-11:04:12.982|INFO|null||***********:6666|||32488|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>scan client executor class start
********-11:04:12.989|INFO|null||***********:6666|||32488|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>scan client executor class end
********-11:04:12.991|INFO|null||***********:6666|||32488|main|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>soma-job use port:9944
********-11:04:14.623|INFO|null||***********:6666|||32488|Thread-34|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=server started, listening on 9944
********-11:04:14.625|INFO|null||***********:6666|||32488|Thread-34|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=...SOMA>>>>>>>>>>>soma Auto registry is not use.  config param [isAutoRegistry]=1
********-11:04:16.720|INFO|null||***********:6666|||32488|main|org.springframework.cloud.commons.util.InetUtils.?.?|MSG=Cannot determine local hostname
********-11:04:16.795|INFO|null||***********:6666|||32488|main|org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver.?.?|MSG=Exposing 1 endpoint(s) beneath base path '/actuator'
********-11:04:16.928|INFO|null||***********:6666|||32488|main|org.apache.coyote.http11.Http11NioProtocol.?.?|MSG=Starting ProtocolHandler ["http-nio-6666"]
********-11:04:16.951|INFO|null||***********:6666|||32488|main|org.springframework.boot.web.embedded.tomcat.TomcatWebServer.?.?|MSG=Tomcat started on port(s): 6666 (http) with context path ''
********-11:04:18.450|INFO|null||***********:6666|||32488|main|com.psbc.cpufp.BranchAgentApplication.?.?|MSG=Started BranchAgentApplication in 19.231 seconds (JVM running for 21.618)
********-11:04:20.822|INFO|null||***********:6666|||32488|RMI TCP Connection(5)-**************|org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].?.?|MSG=Initializing Spring DispatcherServlet 'dispatcherServlet'
********-11:04:20.822|INFO|null||***********:6666|||32488|RMI TCP Connection(5)-**************|org.springframework.web.servlet.DispatcherServlet.?.?|MSG=Initializing Servlet 'dispatcherServlet'
********-11:04:20.826|INFO|null||***********:6666|||32488|RMI TCP Connection(5)-**************|org.springframework.web.servlet.DispatcherServlet.?.?|MSG=Completed initialization in 4 ms
********-11:04:20.828|INFO|null||***********:6666|||32488|RMI TCP Connection(4)-**************|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Starting...
********-11:04:21.673|INFO|null||***********:6666|||32488|RMI TCP Connection(4)-**************|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Start completed.
********-11:47:04.552|INFO|null||***********:6666|||32488|Thread-37|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=*** shutting down soma server since JVM is shutting down
********-11:47:04.617|INFO|null||***********:6666|||32488|Thread-37|org.soma.job.client.adapter.LoggerAdapter.?.?|MSG=*** server shut down
********-11:47:05.602|INFO|null||***********:6666|||32488|SpringApplicationShutdownHook|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Shutdown initiated...
********-11:47:05.636|INFO|null||***********:6666|||32488|SpringApplicationShutdownHook|com.zaxxer.hikari.HikariDataSource.?.?|MSG=HikariPool-1 - Shutdown completed.
