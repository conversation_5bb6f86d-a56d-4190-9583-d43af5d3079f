//package com.psbc.cpufp.entity.enu.tableinfoenum;
//
//import com.psbc.cpufp.common.cache.service.TableInfoInterface;
//
///**
// * 表txCodeMapInfo枚举
// */
//public enum TxCodeMapInfo4RedisEnum implements TableInfoInterface {
//    /**
//     * front_txcode
//     */
//    FRONT_TX_CODE("front_txcode"),
//    /**
//     * outsys_txcode_id
//     */
//    OUT_SYS_TX_CODE_ID("outsys_txcode_id");
//
//    TxCodeMapInfo4RedisEnum(String tableFields) {
//        this.tableFields = tableFields;
//    }
//
//    /**
//     * 字段
//     */
//    private String tableFields;
//
//    @Override
//    public String getTableField() {
//        return tableFields;
//    }
//
//    @Override
//    public String getTableName() {
//        return "tb_txcode_map_info";
//    }
//}
