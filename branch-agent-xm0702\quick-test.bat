@echo off
echo ========================================
echo 快速测试 xmysfzjjg 迁移功能
echo ========================================

echo.
echo 1. 检查应用是否运行...
curl -s http://localhost:6666/test/health > nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 应用正在运行
) else (
    echo ✗ 应用未运行，请先启动应用
    echo   使用命令: mvn spring-boot:run -Dspring.profiles.active=local
    pause
    exit /b 1
)

echo.
echo 2. 测试配置加载...
curl -s http://localhost:6666/test/config

echo.
echo 3. 运行改进的Socket客户端测试...
cd /d "%~dp0"
java -cp "target/classes;target/test-classes" com.psbc.xmysfzjjg.client.ImprovedSocketClientTest

echo.
echo 4. 测试WebService WSDL...
curl -s http://localhost:6666/wservices/IWebServiceService?wsdl | findstr "wsdl:definitions" > nul 2>&1
if %errorlevel% == 0 (
    echo ✓ WebService WSDL 可访问
) else (
    echo ✗ WebService WSDL 不可访问
)

echo.
echo ========================================
echo 快速测试完成！
echo ========================================
pause
