package com.psbc.xmysfzjjg.service;

import com.alibaba.fastjson.JSON;
import com.psbc.xmysfzjjg.util.ConfigService;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Iterator;

/**
 * 消息处理服务
 * 整合原有ServerThread的业务逻辑
 */
@Service
public class MessageProcessService {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageProcessService.class);
    
    @Autowired
    private ConfigService configService;
    
    @Autowired
    private ZhbgService zhbgService;
    
    @Autowired
    private WlHostService wlHostService;
    
    /**
     * 处理接收到的消息
     */
    public String processMessage(String message) throws Exception {
        logger.info("开始处理消息: {}", message);
        
        // 判断消息格式并提取服务号
        String serviceNo = extractServiceNo(message);
        logger.info("提取的服务号: {}", serviceNo);
        
        String response;
        long startTime = System.nanoTime();
        
        if ("30001".equals(serviceNo)) {
            // 调用综合办公30001接口 - JSON格式，获取按揭贷款信息
            logger.info("调用综合办公30001接口，获取按揭贷款信息");
            try {
                response = zhbgService.sendZhbgRequest(message, "30001");
            } catch (Exception e) {
                logger.warn("综合办公30001接口调用失败，返回模拟响应", e);
                response = createMockJsonResponse("30001");
            }

        } else if ("20006".equals(serviceNo)) {
            // 调用综合办公20006接口 - XML格式，查询监管账户变动反馈信息
            logger.info("调用综合办公20006接口，查询监管账户变动反馈信息");
            try {
                response = zhbgService.sendZhbgRequest(message, "20006");
            } catch (Exception e) {
                logger.warn("综合办公20006接口调用失败，返回模拟响应", e);
                response = createMockXmlResponse("20006");
            }

        } else {
            // 原来的逻辑，发往委托方
            logger.info("发往委托方系统处理");
            response = wlHostService.sendToWlHost(message);
        }
        
        long endTime = System.nanoTime();
        long duration = (endTime - startTime) / 1000L / 1000L;
        logger.info("消息处理完成，耗时: {} 毫秒，响应长度: {}", duration, response != null ? response.length() : 0);
        
        return response;
    }
    
    /**
     * 从消息中提取服务号
     */
    private String extractServiceNo(String message) {
        try {
            // 判断是JSON还是XML
            if (isJson(message)) {
                logger.info("检测到JSON格式消息");
                HashMap<String, Object> map = JSON.parseObject(message, HashMap.class);
                Object serviceNo = map.get("serviceno");
                return serviceNo != null ? serviceNo.toString() : null;
                
            } else if (isXml(message)) {
                logger.info("检测到XML格式消息");
                Document doc = DocumentHelper.parseText(message);
                Element root = doc.getRootElement();
                logger.info("XML根节点名称: {}", root.getName());
                
                // 获取根节点下的子节点head
                Iterator<Element> it = root.elementIterator("head");
                while (it.hasNext()) {
                    Element headElement = it.next();
                    String serviceNo = headElement.elementTextTrim("serviceno");
                    if (serviceNo != null) {
                        logger.info("从XML中提取的服务号: {}", serviceNo);
                        return serviceNo;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("提取服务号时发生异常", e);
        }
        
        return null;
    }
    
    /**
     * 判断是否是JSON格式
     */
    private boolean isJson(String value) {
        try {
            JSON.parseObject(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 判断是否是XML格式
     */
    private boolean isXml(String value) {
        try {
            DocumentHelper.parseText(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 创建模拟JSON响应
     */
    private String createMockJsonResponse(String serviceNo) {
        return "{\"status\":\"success\",\"serviceNo\":\"" + serviceNo + "\",\"message\":\"模拟响应\",\"data\":{\"result\":\"ok\",\"timestamp\":" + System.currentTimeMillis() + "}}";
    }

    /**
     * 创建模拟XML响应
     */
    private String createMockXmlResponse(String serviceNo) {
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content>" +
                "<head><statecode>1</statecode><msg>模拟响应成功</msg><serviceno>" + serviceNo + "</serviceno></head>" +
                "<body><data>模拟数据</data><timestamp>" + System.currentTimeMillis() + "</timestamp></body>" +
                "</content>";
    }
}
