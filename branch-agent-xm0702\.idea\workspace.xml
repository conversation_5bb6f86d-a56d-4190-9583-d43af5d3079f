<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0729870b-d22b-4e4d-a99b-d013f67e541c" name="Changes" comment="fix：修改部分代码，解决打包异常">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/psbc/cpufp/BranchAgentApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/psbc/cpufp/BranchAgentApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/dev/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/dev/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/local/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/local/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1750734639" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev" />
                    <option name="lastUsedInstant" value="1748507190" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="C:\workspace\bank\repo_cpufp" />
        <option name="mavenHome" value="$PROJECT_DIR$/../../../tools/apache-maven-3.6.3" />
        <option name="userSettingsFile" value="C:\tools\apache-maven-3.6.3\conf\setting-china.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2xlJx5KIBB9HoKmaYHXFSpl520v" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/workspace/bank/rebuild/branch-agent-xm0702/pom.xml&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager" selected="Spring Boot.BranchAgentApplication">
    <configuration name="SimpleSocketTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.psbc.xmysfzjjg.SimpleSocketTest" />
      <module name="branch-agent" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.psbc.xmysfzjjg.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SocketClientTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.psbc.xmysfzjjg.client.SocketClientTest" />
      <module name="branch-agent" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.psbc.xmysfzjjg.client.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="quick-test" type="BatchConfigurationType" factoryName="Batch" temporary="true">
      <module name="branch-agent" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SCRIPT_NAME" value="quick-test.bat" />
      <option name="PARAMETERS" value="" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="XmysfzjjgApplicationTest.testMessageProcessService (1)" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="branch-agent" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.psbc.xmysfzjjg.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.psbc.xmysfzjjg" />
      <option name="MAIN_CLASS_NAME" value="com.psbc.xmysfzjjg.XmysfzjjgApplicationTest" />
      <option name="METHOD_NAME" value="testMessageProcessService" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="XmysfzjjgApplicationTest.testMessageProcessService" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="branch-agent" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.psbc.xmysfzjjg.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.psbc.xmysfzjjg" />
      <option name="MAIN_CLASS_NAME" value="com.psbc.xmysfzjjg.XmysfzjjgApplicationTest" />
      <option name="METHOD_NAME" value="testMessageProcessService" />
      <option name="TEST_OBJECT" value="method" />
      <option name="VM_PARAMETERS" value="-ea Dspring.config.additional-location=classpath:local/" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="branch-agent-xm0417" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="branch-agent-xm0417" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BranchAgentApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="branch-agent" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.psbc.cpufp.BranchAgentApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.config.additional-location=classpath:local/" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.SimpleSocketTest" />
      <item itemvalue="Application.SocketClientTest" />
      <item itemvalue="Batch.quick-test" />
      <item itemvalue="JUnit.XmysfzjjgApplicationTest.testMessageProcessService (1)" />
      <item itemvalue="JUnit.XmysfzjjgApplicationTest.testMessageProcessService" />
      <item itemvalue="Spring Boot.BranchAgentApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.SocketClientTest" />
        <item itemvalue="Batch.quick-test" />
        <item itemvalue="Application.SimpleSocketTest" />
        <item itemvalue="JUnit.XmysfzjjgApplicationTest.testMessageProcessService (1)" />
        <item itemvalue="JUnit.XmysfzjjgApplicationTest.testMessageProcessService" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0729870b-d22b-4e4d-a99b-d013f67e541c" name="Changes" comment="" />
      <created>1748506971235</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748506971235</updated>
      <workItem from="1748506972793" duration="10421000" />
      <workItem from="1748570219977" duration="19852000" />
      <workItem from="1748912179796" duration="5069000" />
      <workItem from="1748919470143" duration="38000" />
      <workItem from="1748920057875" duration="42000" />
      <workItem from="1748920287746" duration="731000" />
      <workItem from="1748934327639" duration="650000" />
      <workItem from="1750648259838" duration="1086000" />
      <workItem from="1750734194851" duration="5928000" />
      <workItem from="1750905550360" duration="346000" />
      <workItem from="1750905995472" duration="1000000" />
      <workItem from="1750991671924" duration="1218000" />
      <workItem from="1751011212995" duration="6705000" />
      <workItem from="1751420072465" duration="8033000" />
      <workItem from="1751432292255" duration="3723000" />
    </task>
    <task id="LOCAL-00001" summary="fix：修改部分代码，解决打包异常">
      <option name="closed" value="true" />
      <created>1748590748398</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748590748399</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix：修改部分代码，解决打包异常" />
    <option name="LAST_COMMIT_MESSAGE" value="fix：修改部分代码，解决打包异常" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>