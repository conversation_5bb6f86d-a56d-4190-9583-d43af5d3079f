package com.psbc.xmysfzjjg.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;

/**
 * WebService接口定义
 */
@WebService(targetNamespace = "http://webservice.psbc.com/")
public interface IWebService {
    
    @WebMethod
    @WebResult(targetNamespace = "http://webservice.psbc.com/")
    String sayHello(@WebParam(targetNamespace = "http://webservice.psbc.com/") String name);
    
    @WebMethod
    @WebResult(targetNamespace = "http://webservice.psbc.com/")
    String Execute(@WebParam(targetNamespace = "http://webservice.psbc.com/") String bankId, 
                   @WebParam(targetNamespace = "http://webservice.psbc.com/") String inParameter);
}
