//package com.psbc.cpufp.util;
//
//import cn.hutool.core.util.IdUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.psbc.cpufp.entity.jyt.JytInterConfMaintence;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR> Ye
// * @version 1.0.0
// * @title ParseJsonToBean
// * @description jsonStr 转配置对象
// * @create 2025/4/14 22:03
// **/
//
//@Component
//public class ParseJsonToBean {
//
//    private static final List<JytInterConfMaintence> JYT_INTER_CONF_MAINTENCES = new ArrayList<>();
//
//    /**
//     * jsonStr 转配置对象
//     *
//     * @param jsonStr json字符串
//     * @return JytInterConfMaintence配置对象
//     */
//    public List<JytInterConfMaintence> parseJson(String jsonStr) {
//        JSONObject jsonObject = JSON.parseObject(jsonStr);
//        traverseJsonObject(jsonObject, "0");
//        return JYT_INTER_CONF_MAINTENCES;
//    }
//
//
//    /**
//     * 解析数据
//     *
//     * @param jsonObject 字符串转成的jsonObject对象
//     * @param pid        父级id
//     */
//    public void traverseJsonObject(JSONObject jsonObject, String pid) {
//
//        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
//            String id = IdUtil.fastSimpleUUID();
//            Object value = entry.getValue();
//            String key = entry.getKey();
//
//            if (value instanceof JSONObject) {
//                // 如果是JSONObject，递归遍历
//                JytInterConfMaintence jytInterConfMaintence = new JytInterConfMaintence();
//                jytInterConfMaintence.setId(id);
//                jytInterConfMaintence.setMsgUpNode(pid);
//                jytInterConfMaintence.setMsgNodeCode(key);
//                JYT_INTER_CONF_MAINTENCES.add(jytInterConfMaintence);
//                traverseJsonObject((JSONObject) value, id);
//            } else if (value instanceof JSONArray) {
//                // 如果是JSONArray，遍历数组中的每个元素
//                JSONArray array = (JSONArray) value;
//                for (int i = 0; i < array.size(); i++) {
//                    traverseJsonObject(array.getJSONObject(i), id);
//                }
//            } else {
//                // 如果是基本类型，直接打印
//                JytInterConfMaintence jytInterConfMaintence = new JytInterConfMaintence();
//                jytInterConfMaintence.setId(id);
//                jytInterConfMaintence.setMsgUpNode(pid);
//                jytInterConfMaintence.setMsgNodeCode(key);
//                jytInterConfMaintence.setNodeValue(value.toString());
//                JYT_INTER_CONF_MAINTENCES.add(jytInterConfMaintence);
//            }
//        }
//    }
//}
