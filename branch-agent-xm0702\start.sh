#!/bin/bash

echo "========================================"
echo "Starting Branch Agent with xmysfzjjg"
echo "========================================"

# 设置Java环境变量（如果需要）
# export JAVA_HOME=/usr/lib/jvm/java-8-openjdk

# 设置应用参数
export SPRING_PROFILES_ACTIVE=local
export SERVER_PORT=6666

# 启动应用
echo "Starting application..."
java -jar target/branch-agent.jar \
  --spring.profiles.active=$SPRING_PROFILES_ACTIVE \
  --server.port=$SERVER_PORT \
  --logging.level.com.psbc.xmysfzjjg=DEBUG

echo "Application stopped."
