# XMYSFZJJG项目迁移文档

## 项目概述

本项目将原有的xmysfzjjg项目完整迁移并重构为符合branch-agent-xm0702框架的SpringBoot项目。

## 迁移内容

### 1. 架构变更
- **原架构**: 传统Java Web项目 + CXF WebService + 多线程Socket
- **新架构**: SpringBoot + Netty + REST API + WebService

### 2. 主要改进
- 将原有的ServerThread多线程Socket服务重构为Netty高性能网络服务
- 将properties配置文件整合到application.yml
- 使用@ConfigurationProperties替代工具类读取配置
- 保持原有WebService功能的同时增加REST API支持
- 统一日志框架为Logback

## 项目结构

```
src/main/java/com/psbc/xmysfzjjg/
├── config/                     # 配置类
│   ├── XmysfzjjgProperties.java    # 基础配置属性
│   ├── ZhbgProperties.java         # 综合办公配置属性
│   ├── NettyProperties.java        # Netty配置属性
│   ├── WebServiceProperties.java   # WebService配置属性
│   ├── WebServiceConfig.java       # WebService配置类
│   └── XmysfzjjgAutoConfiguration.java # 自动配置类
├── controller/                  # REST控制器
│   ├── ZhbgController.java         # 综合办公接口控制器
│   └── TestController.java         # 测试控制器
├── netty/                      # Netty网络服务
│   ├── NettySocketServer.java      # Netty Socket服务器
│   └── SocketMessageHandler.java   # Socket消息处理器
├── service/                    # 业务服务
│   ├── MessageProcessService.java  # 消息处理服务
│   ├── ZhbgService.java            # 综合办公服务
│   └── WlHostService.java          # 外联主机服务
├── webservice/                 # WebService
│   ├── IWebService.java            # WebService接口
│   ├── WebServiceImpl.java         # WebService实现
│   └── ClientIWebService.java      # WebService客户端接口
├── util/                       # 工具类
│   ├── ConfigService.java          # 配置服务
│   └── MessageUtil.java            # 消息工具类
└── listener/                   # 监听器
    └── ApplicationStartupListener.java # 应用启动监听器
```

## 配置说明

### application.yml配置项

```yaml
xmysfzjjg:
  # 基础配置
  ip: 127.0.0.1                 # Socket服务IP
  port: 9702                    # Socket服务端口
  wg-port: 8888                 # 外联服务端口
  http-port: 9999               # HTTP服务端口
  
  # 综合办公配置
  zhbg:
    post-url: http://**************:9090/  # 综合办公基础URL
    url30001: api/admin/xzp/queryYxjfLsxdye # 30001接口路径
    url20006: api/admin/xzp/queryJgzhInfo   # 20006接口路径
    
  # WebService配置
  webservice:
    client:
      address: https://*************:8088/JYDJService/JYDJService1.asmx?wsdl
      
  # Netty服务器配置
  netty:
    socket-port: 8888           # Socket服务端口
    http-port: 9999             # HTTP服务端口
    worker-threads: 10          # 工作线程数
    connect-timeout: 30         # 连接超时时间(秒)
```

## 服务端点

### 1. WebService端点
- **地址**: `http://localhost:6666/wservices/IWebServiceService?wsdl`
- **方法**: 
  - `sayHello(String name)`: 测试方法
  - `Execute(String bankId, String inParameter)`: 主要业务方法

### 2. REST API端点
- **基础路径**: `http://localhost:6666`
- **接口列表**:
  - `GET /test/health`: 健康检查
  - `GET /test/config`: 配置信息查看
  - `POST /api/admin/xzp/queryYxjfLsxdye`: 查询按揭贷款信息（30001）
  - `POST /api/admin/xzp/queryJgzhInfo`: 查询监管账户信息（20006）

### 3. Socket服务
- **端口**: 8888
- **协议**: TCP
- **消息格式**: 支持JSON和XML格式
- **服务号**:
  - `30001`: 综合办公按揭贷款查询（JSON）
  - `20006`: 综合办公监管账户查询（XML）
  - 其他: 转发到委托方系统

## 启动方式

### 1. 开发环境启动
```bash
mvn spring-boot:run -Dspring.profiles.active=local
```

### 2. 生产环境启动
```bash
java -jar branch-agent.jar --spring.profiles.active=dev
```

## 测试方法

### 1. 单元测试
```bash
mvn test
```

### 2. Socket客户端测试
运行测试类: `com.psbc.xmysfzjjg.client.SocketClientTest`

### 3. HTTP接口测试
运行测试类: `com.psbc.xmysfzjjg.client.HttpClientTest`

### 4. WebService测试
访问WSDL地址: `http://localhost:6666/wservices/IWebServiceService?wsdl`

## 迁移对比

| 功能 | 原实现 | 新实现 | 改进点 |
|------|--------|--------|--------|
| Socket服务 | ServerThread多线程 | Netty异步IO | 高性能、低资源消耗 |
| 配置管理 | Properties文件+工具类 | application.yml+@ConfigurationProperties | 类型安全、IDE支持 |
| 日志 | Log4j | Logback | 更好的性能和配置 |
| WebService | CXF独立配置 | SpringBoot集成CXF | 统一管理、自动配置 |
| HTTP服务 | HttpServer | SpringBoot内嵌Tomcat | 更好的生态和监控 |

## 注意事项

1. **端口配置**: 确保配置的端口未被占用
2. **SSL证书**: WebService客户端已配置信任所有证书
3. **网络连接**: 综合办公和委托方系统的网络连通性
4. **日志级别**: 可通过application.yml调整日志级别
5. **监控**: 已集成Actuator，可通过/actuator端点监控应用状态

## 故障排查

### 1. 启动失败
- 检查端口是否被占用
- 检查配置文件格式是否正确
- 查看启动日志中的错误信息

### 2. Socket连接失败
- 检查防火墙设置
- 确认Netty服务器是否正常启动
- 检查客户端连接参数

### 3. WebService调用失败
- 检查目标服务是否可达
- 确认SSL证书配置
- 查看详细的异常日志

## 后续优化建议

1. **性能监控**: 集成Micrometer进行性能监控
2. **配置中心**: 考虑使用Nacos等配置中心
3. **服务发现**: 集成Eureka或Consul
4. **链路追踪**: 集成Sleuth进行分布式链路追踪
5. **容器化**: 制作Docker镜像便于部署
