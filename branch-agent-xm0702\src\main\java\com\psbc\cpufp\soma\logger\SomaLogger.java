//package com.psbc.cpufp.soma.logger;
//
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.soma.job.client.adapter.LoggerAdapter;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//
///**
// * 日志适配器路径(可选，如果不配置日志打印方式默认为控制台输出)
// *
// * <AUTHOR>
// */
//@Component
//public class SomaLogger extends LoggerAdapter {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(SomaLogger.class);
//
//    private static boolean logLevel;
//
//    @Value("${somaInfoLogSwitch:false}")
//    public void setLogLevel(boolean logLevel) {
//        setLog(logLevel);
//    }
//
//    public static void setLog(boolean level) {
//        SomaLogger.logLevel = level;
//    }
//
//    @Override
//    public void showLog(String log) {
//        if (logLevel) {
//            LOGGER.info(log);
//        }
//    }
//}
