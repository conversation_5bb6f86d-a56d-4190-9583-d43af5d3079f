package com.psbc.xmysfzjjg.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * xmysfzjjg项目配置属性类
 * 替代原有的ConfigUtil
 */
@Component
@ConfigurationProperties(prefix = "xmysfzjjg")
public class XmysfzjjgProperties {
    
    /**
     * Socket服务IP地址
     */
    private String ip = "127.0.0.1";
    
    /**
     * Socket服务端口
     */
    private Integer port = 9702;
    
    /**
     * 外联服务端口
     */
    private Integer wgPort = 8888;
    
    /**
     * HTTP服务端口
     */
    private Integer httpPort = 9999;
    
    /**
     * 默认响应消息
     */
    private String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?><content><head><statecode>1</statecode><msg>交易成功</msg></head><body><table_account><row><instructionno>*************</instructionno><issuccess>1</issuccess></row></table_account></body></content>";

    // Getters and Setters
    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Integer getWgPort() {
        return wgPort;
    }

    public void setWgPort(Integer wgPort) {
        this.wgPort = wgPort;
    }

    public Integer getHttpPort() {
        return httpPort;
    }

    public void setHttpPort(Integer httpPort) {
        this.httpPort = httpPort;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
