package com.psbc.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import org.springframework.stereotype.Service;

@Service
@WebService(targetNamespace = "http://tempuri.org/")
public interface ClientIWebService {
  @WebMethod(operationName = "Execute", action = "http://tempuri.org/Execute")
  @WebResult(name = "ExecuteResult", targetNamespace = "http://tempuri.org/")
  String Execute(@WebParam(targetNamespace = "http://tempuri.org/", name = "BankID") String paramString1, @WebParam(targetNamespace = "http://tempuri.org/", name = "inParmeter") String paramString2);
}
