//package com.psbc.cpufp.config;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//
//import javax.sql.DataSource;
//
///**
// * 数据源1加载
// *
// * <AUTHOR>
// */
//@Configuration
//@MapperScan(value = {"com.psbc.cpufp.mapper.mapperone.**"}, sqlSessionFactoryRef = "oneSqlSessionFactory")
//public class DataSourceOneConfig {
//
//    /**
//     * druid连接池
//     *
//     * @return 返回连接对象
//     */
//    @Primary
//    @Bean("oneDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.one")
//    public DataSource druid1() {
//        return new DruidDataSource();
//    }
//
//    /**
//     * 创建数据源一的SqlSessionFactory
//     *
//     * @param dataSource 数据源
//     * @return 数据源一的SqlSessionFactory
//     * @throws Exception 异常
//     */
//    @Bean(name = "oneSqlSessionFactory")
//    public SqlSessionFactory sqlSessionFactory(@Qualifier("oneDataSource") DataSource dataSource) throws Exception {
//        SqlSessionFactoryBean sessionFactoryBean = new SqlSessionFactoryBean();
//        sessionFactoryBean.setDataSource(dataSource);
//        sessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver()
//                .getResources("classpath*:mapperOne/*.xml"));
//        return sessionFactoryBean.getObject();
//    }
//
//    /**
//     * 创建数据源一的SqlSessionTemplate
//     *
//     * @param sqlSessionFactory sqlSessionFactory
//     * @return SqlSessionTemplate
//     */
//    @Bean(name = "oneSqlSessionTemplate")
//    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("oneSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//}
