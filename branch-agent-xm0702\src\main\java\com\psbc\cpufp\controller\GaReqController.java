//package com.psbc.cpufp.controller;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.JsonNode;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.psbc.cpufp.entity.jyt.JytInterLog;
//import com.psbc.cpufp.util.*;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpHeaders;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import javax.xml.bind.DatatypeConverter;
//import java.io.UnsupportedEncodingException;
//import java.nio.charset.StandardCharsets;
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Random;
//
///**
// * 总行接口测试
// */
//@RestController
//@Slf4j
//public class GaReqController {
//
//    private static final Logger log = LoggerFactory.getLogger(GaReqController.class);
//
//
//    @Resource
//    private EncryptAndDecryptTools encryptAndDecryptTools;
//
//    // 安全等级10报文工具类
//    @Resource
//    private PsbcBranchSecurity10Service psbcBranchSecurity10Service;
//
//    //sm2加解密工具类
//    @Resource
//    private Sm2Util sm2Util;
//
//
//
//    /**
//         * 请求分行前置卡开户信息接口
//     */
//    @Value("${jyt.bankUrl.601087Url}")
//    private String kakaihuUrl;
//
//    /**
//     * 请求分行前置三要素信息接口
//     */
//    @Value("${jyt.bankUrl.600368Url}")
//    private String accInfoInquiryUrl;
//
//
//    /**
//     * 请求方jyt 系统号
//     */
//    @Value("${jyt.reqSysCode}")
//    private String reqSysCode;
//
//    /**
//     * 请求方jyt 证书序列号
//     */
//    @Value("${jyt.userCert.userCertSn}")
//    private String userCertSn;
//
//    /**
//     * 请求方jyt 证书公钥
//     */
//    @Value("${jyt.userCert.userCertPublicKey}")
//    private String userCertPublicKey;
//
//    /**
//     * 请求方jyt 证书私钥
//     */
//    @Value("${jyt.userCert.userCertPrivateKey}")
//    private String userCertPrivateKey;
//
//    /**
//     * ga 证书私钥
//     */
//    @Value("${jyt.gongAn.gaCertPublicKey}")
//    private String gaCertPublicKey;
//
//
//    /**
//     * 公安请求加密
//     *
//     * @param reqBody 请求报文
//     * @param headers 请求header
//     * @return 响应结果
//     */
//    @PostMapping("/ga/jiami")
//    public String gaJiami(@RequestBody JSONObject reqBody, @RequestHeader MultiValueMap<String, String> headers) {
//        Map<String, Object> gaHead = new HashMap<>();
//        gaHead.put("TxCode", "0305");
//        gaHead.put("Mode", "01");
//        gaHead.put("TransSerialNumber", "************_2025012018289223796167861415954549123978");
//        gaHead.put("From", "************");
//        gaHead.put("To", "************");
//
//        Map<String, Object> gaContent = new HashMap<>();
//        gaContent.put("StartTime", "2025-04-16");
//        gaContent.put("EndTime", "2025-04-16");
//        gaContent.put("AccountType", 1);
//        gaContent.put("IdType", "001");
//        gaContent.put("AccountCredentialNumber", "352229198727364532");
//        gaContent.put("Acount", ""); //卡号？
//        gaContent.put("ExtendedFields", "");
//        gaContent.put("ExtendedFields1", "");
//        gaContent.put("ExtendedFields2", "");
//        gaContent.put("ExtendedFields3", "");
//        gaContent.put("ExtendedFields4", "");
//        gaContent.put("Remarks", "备注");
//
//        Map<String, Object> gaBody = new HashMap<>();
//        //目前明文，要转成base64编码
//        gaBody.put("Content", gaContent);
//        //数字信封密文
//        gaBody.put("SignatureValue", "");
//
//        Map<String, Object> gaAttachments = new HashMap<>();
//        //文件名,参考附录O.1
//        gaAttachments.put("Filename", null);
//        //文件加密base64码
//        gaAttachments.put("Content", null);
//        //数字信封密文
//        gaAttachments.put("SignatureValue", null);
//        //算法名称
//        gaAttachments.put("SignatureMethod", "SM2");
//
//        Map<String, Object> respMap = new HashMap<>();
//        respMap.put("Head", gaHead);
//        respMap.put("Body", gaBody);
//        respMap.put("Attachments", gaAttachments);
//        respMap.put("SignatureMethod", "SM2");
//
//        JSONObject gaReqInfo = new JSONObject(respMap); //公安请求报文
//        System.out.println("ga请求报文明文：" + gaReqInfo.toJSONString());
//
//        JSONObject rbody = gaReqInfo.getJSONObject("Body");
//        BodyModel bodyModel = null;
//        try {
//            //公安明文加密
//            bodyModel = encryptAndDecryptTools.encryptMyDataToBankByPublicKey(rbody.toString());
//        } catch (UnsupportedEncodingException e) {
//            throw new RuntimeException(e);
//        }
//        System.out.println("加密报文：{},{}" + bodyModel.getContent() + "  , 第SM2-----:" + bodyModel.getSignatureValue());
//
//        return "加密报文：{},{}" + bodyModel.getContent() + "  , 第SM2-----:" + bodyModel.getSignatureValue();
//
//    }
//
//
//    /**
//     * 查询三要素接口获得返回值
//     *
//     * @param reqBody 请求报文
//     * @param headers 请求header
//     * @return 响应结果
//     */
//    @PostMapping("/ga/sanyaosu")
//    public JSONObject gaSanyaosu(@RequestBody JSONObject reqBody, @RequestHeader MultiValueMap<String, String> headers) {
//        log.info("进入/accInfoInquiry接口，请求参数reqBody:{}, headers:{}", reqBody.toJSONString(), headers.toString());
//        // 记录日志
//        JytInterLog jytInterLog = new JytInterLog();
//        jytInterLog.setRequester("GA");
//        jytInterLog.setResponder("JYT");
//        jytInterLog.setReqInter("jyt账户信息查询接口");
//        jytInterLog.setReqInterUrl("/accInfoInquiry");
//        jytInterLog.setReqParam("请求参数：" + reqBody.toJSONString());
//        jytInterLog.setAttrAddr("");
//        jytInterLog.setOthMsg1("公安发起请求查询账户信息");
//        // 0-成功，1-失败
//        String reqRes = "1";
//        String reqDesc = "进入接口还未开始请求";
//        try {
//            // 公安请求报文加密-测试
//            //-----------测试加解密-----------
//            //            String reqBody1 = reqBody.get("Body").toString();
//            //            BodyModel bodyModel1 = encryptAndDecryptTools.encryptMyDataToBankByPublicKey(reqBody1);
//
//            BodyModel bodyModel2 = new BodyModel();
//            JSONObject o = (JSONObject) JSON.toJSON(reqBody.get("Body"));
//            Object content = o.get("Content");
//            if (content != null) {
//                bodyModel2.setContent(content.toString());
//            } else {
//                log.error("Content空");
//            }
//            Object signatureValue = o.get("SignatureValue");
//            if (signatureValue != null) {
//                bodyModel2.setSignatureValue(signatureValue.toString());
//            } else {
//                log.error("SignatureValue空");
//            }
//            String s = encryptAndDecryptTools.decryptData(bodyModel2);
//            System.out.println("解密后的公安请求报文，body部分:" + s);
//            //组装完整的报文
//            Map<String, Object> gaBody = new HashMap<>();
//            gaBody.put("Body", s);
//
//            JSONObject reqHead = (JSONObject) JSON.toJSON(reqBody.get("Head"));
//            Map<String, Object> gaHead = new HashMap<>();
//            gaHead.put("Head", reqHead);
//
//            Map<String, Object> respMap = new HashMap<>();
//            respMap.put("Head", gaHead);
//            respMap.put("Body", gaBody);
//            JSONObject remoteRequestBody = new JSONObject(respMap);
//            System.out.println("ga解密后的明文请求：" + remoteRequestBody.toJSONString());
//
//            //-----------测试加解密-----------
//            // 公安请求报文（解密）
//            //JSONObject remoteRequestBody = doDec(reqBody);
//            // remoteRequestBody - 解密后的公安请求报文 , 请求分行前置接口-卡开户信息（对应总行接口601087）-加密报文，组装并发送请求给分行前置，获取返回报文
//            //String responseEntityQianzhi = doZonghangEnc(remoteRequestBody, headers);
//            // 解析总行响应（解密）明文
//            //JSONObject zhRespInfo = doZonhangDec(responseEntityQianzhi);
//            JSONObject zhRespInfo = new JSONObject();
//            // 将总行结果处理成发往公安的合规报文，明文
//            JSONObject toGongAn = doChangeToGongAn(zhRespInfo, "accInfoInquiry");
//            // 向公安请求的报文，响应给公安的报文（加密）
//            JSONObject resultResBody = doEnc600386(toGongAn);
//            // 将结果响应给公安
//            // 响应结果http请求发往北接出端口9901
//            reqRes = "0";
//            reqDesc = "账户信息查询成功";
//            return resultResBody;
//        } catch (Exception e) {
//            log.error("EncryptMessage failed | Error: {}", e.getMessage());
//            reqRes = "1";
//            reqDesc = "接口请求异常：" + e.getCause();
//            throw new RuntimeException("Encryption process failed", e);
//        } finally {
//            jytInterLog.setRespRes(reqRes);
//            jytInterLog.setRespDesc(reqDesc);
//            log.info(jytInterLog.toString());
//            //            boolean logFlag = jytInterLogService.insertLog(jytInterLog);
//            //            if (logFlag) {
//            //                log.info("成功记录日志，{}", jytInterLog.toString());
//            //            } else {
//            //                log.error("记录日志失败, 在控制台打印：{}", jytInterLog.toString());
//            //            }
//        }
//    }
//
//
//    /**
//     * 查询卡开户接口获得返回值
//     *
//     * @param reqBody 请求报文
//     * @param headers 请求header
//     * @return 响应结果
//     */
//    @PostMapping("/ga/kakaihu")
//    public JSONObject cardInstall(@RequestBody JSONObject reqBody, @RequestHeader MultiValueMap<String, String> headers) {
//        log.info("进入/ga/kakaihu 接口，请求参数reqBody:{}, headers:{}", reqBody.toJSONString(), headers.toString());
//        try {
//            // 公安请求报文（解密）
//            JSONObject remoteRequestBody = doDec(reqBody);
//            return remoteRequestBody;
//        } catch (Exception e) {
//            log.error("EncryptMessage failed | Error: {}", e.getMessage());
//            throw new RuntimeException("Encryption process failed", e);
//        } finally {
//            log.info("请求卡开户接口结束！");
//        }
//
//    }
//
//    /**
//     *拼接报文 发往公安 -三要素-账户信息查询
//     */
//    private JSONObject doChangeToGongAn600368(JSONObject responseInfo, String type) {
//        JSONObject respToGa = null;
//        if (type.equals("accInfoInquiry")) {
//            log.info("开始处理前置响应报文...");
//            String servRespCd = responseInfo.get("servRespCd").toString();
//            log.info("servRespCd:{}", servRespCd);
//            String servRespDescInfo = responseInfo.get("servRespDescInfo").toString();
//            log.info("servRespDescInfo:{}", servRespDescInfo);
//            String custNm = responseInfo.get("custNm").toString();
//            log.info("custNm:{}", custNm);
//            String custNo = responseInfo.get("custNo").toString();
//            log.info("custNo:{}", custNo);
//            String conter1TelNo = responseInfo.get("conter1TelNo").toString();
//            String conter2TelNo = responseInfo.get("conter2TelNo").toString();
//            String contAddr = responseInfo.get("contAddr").toString();
//            String commAddr = responseInfo.get("commAddr").toString();
//            log.info("conter1TelNo:{}", conter1TelNo);
//            log.info("conter2TelNo:{}", conter2TelNo);
//            log.info("contAddr:{}", contAddr);
//            log.info("commAddr:{}", commAddr);
//            //提取List数组
//            JSONArray userList = responseInfo.getJSONArray("fieldItemInfo");
//            if (userList == null || userList.isEmpty()) {
//                throw new RuntimeException("userList 为空或不存在");
//            }
//            JSONArray gaContentList = new JSONArray();
//            // 遍历每个用户对象
//            for (int i = 0; i < userList.size(); i++) {
//                JSONObject user = userList.getJSONObject(i); // 获取第 i 个对象
//                String mediumNo = user.getString("mediumNo"); //介质编号 //String relMediumNo = user.getString("relMediumNo"); //关联介质编号
//                String mediumTpCd = user.getString("mediumTpCd"); //介质类型代码
//                String openaccDate = user.getString("openaccDate"); //开户日期,date,YYYYMMDD
//                Double accBal = user.getDouble("accBal"); //账户余额,double //Double accAvalBal = user.getDouble("accAvalBal"); //账户可用余额,double
//                //String persDepAccKindCd = user.getString("persDepAccKindCd"); //个人存款账户种类代码
//                String openAccInstNo = user.getString("openAccInstNo"); //开户机构号
//                String openAccInstName = user.getString("openAccInstName"); //开户机构名称
//                String persDepAccStaFlagCd = user.getString("persDepAccStaFlagCd"); //个人存款账户状态标志码
//                Map<String, Object> gaContent = new HashMap<>();
//                //客户名称
//                gaContent.put("AccountSubjectName", custNm);
//                // 证件号码
//                gaContent.put("TransactionRemark", "从请求参数获取");
//                //"账号"
//                gaContent.put("AccountNumber", mediumNo);
//                //"账户类型"
//                gaContent.put("RiskCode", mediumTpCd);
//                //"账户余额"
//                gaContent.put("AvailableBalance", accBal);
//                //"开户日期"
//                gaContent.put("AccountOpenTime", openaccDate);
//                //"开户银行"
//                gaContent.put("BankName", openAccInstName);
//                //"开户网点"
//                gaContent.put("DepositBankBranch", openAccInstNo);
//                //"账户状态"
//                gaContent.put("AccountStatus", persDepAccStaFlagCd);
//                gaContent.put("ExtendedFields", "扩展字段");
//                gaContent.put("ExtendedFields1", "扩展字段1");
//                gaContent.put("ExtendedFields2", "扩展字段2");
//                gaContent.put("ExtendedFields3", "扩展字段3");
//                gaContent.put("ExtendedFields4", "扩展字段4");
//                gaContent.put("Remarks", "备注");
//                gaContentList.add(gaContent);
//            }
//
//            log.info("开始转换成公安响应报文明文...");
//            Map<String, Object> gaHead = new HashMap<>();
//            gaHead.put("TxCode", "0305"); // 报文类型编码
//            gaHead.put("Code", servRespCd); // 响应码
//            gaHead.put("Description", servRespDescInfo); // 响应状态
//            gaHead.put("data", gaContentList); // 返回结果数据
//            gaHead.put("TransSerialNumber", "从请求参数获取"); // 交易跟踪码
//            gaHead.put("From", ""); // 发送机构ID
//            gaHead.put("To", "");  // 接收机构ID
//
//            Map<String, Object> gaAttachments = new HashMap<>();
//            // 文件名,参考附录O.1
//            gaAttachments.put("Filename", null);
//            // 文件加密base64码
//            gaAttachments.put("Content", null);
//            // 数字信封密文
//            gaAttachments.put("SignatureValue", null);
//            // 算法名称
//            gaAttachments.put("SignatureMethod", null);
//
//            Map<String, Object> gaBody = new HashMap<>();
//            // 目前明文，要转成base64编码
//            gaBody.put("Content", gaContentList);
//            // 数字信封密文
//            gaBody.put("SignatureValue", "");
//            gaBody.put("Attachments", gaAttachments);
//
//            Map<String, Object> respMap = new HashMap<>();
//            respMap.put("Head", gaHead);
//            respMap.put("Body", gaBody);
//            respToGa = new JSONObject(respMap);
//            return respToGa;
//        }
//        return respToGa;
//    }
//
//    /**
//     *拼接报文 发往公安 -卡开户-主体信息
//     */
//    private JSONObject doChangeToGongAn(JSONObject responseInfo, String type) {
//        JSONObject respToGa = null;
//        if (type.equals("accInfoInquiry")) {
//            log.info("开始处理前置响应报文...");
//            JSONObject head = responseInfo.getJSONObject("txHead");
//            String servRespCd = head.get("servRespCd").toString();
//            String servRespDescInfo = head.get("servRespDescInfo").toString();
//            log.info("servRespCd:{},servRespDescInfo:{}", servRespCd, servRespDescInfo);
//            JSONObject body = responseInfo.getJSONObject("txBody");
//            JSONObject entity = body.getJSONObject("txEntity");
//            // 客户名称
//            String custNm = entity.getString("custNm");
//            log.info("custNm:{}", custNm);
//            // 个人证件号码
//            String personalCertNo = entity.getString("personalCertNo");
//            log.info("personalCertNo:{}", personalCertNo);
//            // 介质编号
//            String mediumNo = entity.getString("mediumNo");
//            log.info("mediumNo:{}", mediumNo);
//            // 介质类型代码
//            String mediumTpCd = entity.getString("mediumTpCd");
//            log.info("mediumTpCd:{}", mediumTpCd);
//            // 账户余额
//            String accBal = entity.getString("accBal");
//            log.info("accBal:{}", accBal);
//            // 开户日期
//            String openaccDate = entity.getString("openaccDate");
//            log.info("openaccDate:{}", openaccDate);
//            // 开户机构名称
//            String openAccInstName = entity.getString("openAccInstName");
//            log.info("openAccInstName:{}", openAccInstName);
//            // 开户机构号
//            String openAccInstNo = entity.getString("openAccInstNo");
//            log.info("openAccInstNo:{}", openAccInstNo);
//            // 个人存款账户状态标志码
//            String persDepAccStaFlagCd = entity.getString("persDepAccStaFlagCd");
//            log.info("persDepAccStaFlagCd:{}", persDepAccStaFlagCd);
//            log.info("开始转换成公安响应报文明文...");
//            Map<String, Object> gaHead = new HashMap<>();
//            gaHead.put("TxCode", "0305"); // 报文类型编码
//            gaHead.put("Code", servRespCd); // 响应码
//            gaHead.put("Description", servRespDescInfo); // 响应状态
//            gaHead.put("data", ""); // 返回结果数据
//            gaHead.put("TransSerialNumber", ""); // 交易跟踪码
//            gaHead.put("From", ""); // 发送机构ID
//            gaHead.put("To", "");  // 接收机构ID
//
//            Map<String, Object> gaContent = new HashMap<>();
//            // 客户名称
//            gaContent.put("AccountSubjectName", custNm);
//            // 证件号码
//            gaContent.put("TransactionRemark", personalCertNo);
//            //"账号"
//            gaContent.put("AccountNumber", mediumNo);
//            //"账户类型"
//            gaContent.put("RiskCode", mediumTpCd);
//            //"账户余额"
//            gaContent.put("AvailableBalance", accBal);
//            //"开户日期"
//            gaContent.put("AccountOpenTime", openaccDate);
//            //"开户银行"
//            gaContent.put("BankName", openAccInstName);
//            //"开户网点"
//            gaContent.put("DepositBankBranch", openAccInstNo);
//            //"账户状态"
//            gaContent.put("AccountStatus", persDepAccStaFlagCd);
//            gaContent.put("ExtendedFields", "扩展字段");
//            gaContent.put("ExtendedFields1", "扩展字段1");
//            gaContent.put("ExtendedFields2", "扩展字段2");
//            gaContent.put("ExtendedFields3", "扩展字段3");
//            gaContent.put("ExtendedFields4", "扩展字段4");
//            gaContent.put("Remarks", "备注");
//
//            Map<String, Object> gaAttachments = new HashMap<>();
//            // 文件名,参考附录O.1
//            gaAttachments.put("Filename", null);
//            // 文件加密base64码
//            gaAttachments.put("Content", null);
//            // 数字信封密文
//            gaAttachments.put("SignatureValue", null);
//            // 算法名称
//            gaAttachments.put("SignatureMethod", null);
//
//            Map<String, Object> gaBody = new HashMap<>();
//            // 目前明文，要转成base64编码
//            gaBody.put("Content", gaContent);
//            // 数字信封密文
//            gaBody.put("SignatureValue", "");
//            gaBody.put("Attachments", gaAttachments);
//
//            Map<String, Object> respMap = new HashMap<>();
//            respMap.put("Head", gaHead);
//            respMap.put("Body", gaBody);
//            respToGa = new JSONObject(respMap);
//            return respToGa;
//        }
//        return respToGa;
//    }
//
//
//
//
//
//    //===========================公用相关方法===============================
//    /**
//     * 获取系统跟踪号
//     *
//     * @param bankType      行外系统-out，行内系统-in
//     * @param reqSysCodeStr 系统号
//     * @return 返回系统跟踪号，全局使用
//     */
//    public static String getLsh(String bankType, String reqSysCodeStr) {
//        // 行外系统：如果接入系统代码是12位，则系统跟踪号（32 位）= 时间戳（14 位）  +   接入系统代码（12 位）  +   6 位唯一序列号。
//        // 如果接入系统代码是11位，则系统跟踪号（32 位）= 时间戳（14 位）  +   接入系统代码（前7 位）  +   5位实例序号   +   6位唯一序列号。
//        if (bankType.isEmpty()) { // 为空，默认为行外系统
//            bankType = "out";
//        }
//        if (reqSysCodeStr.isEmpty()) {
//            return "系统号参数为空";
//        }
//        int reqSysCodeLen = reqSysCodeStr.length();
//        String reqSysCode7 = reqSysCodeStr.substring(7);
//        // 6位随机数
//        int kk = new Random().nextInt(999999);
//        String timeNo = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
//        // 默认流水，当为空时候暂时写死，后续需要修改
//        String liushui = "20250304100620135000101871921749";
//        if ("out".equals(bankType)) {
//            if (reqSysCodeLen == 11) {
//                liushui = timeNo + reqSysCodeStr + String.format("%06d", kk);
//            } else if (reqSysCodeLen == 12) {
//                liushui = timeNo + reqSysCode7 + "J0001" + String.format("%06d", kk);
//            }
//        }
//        log.info("拼接成的系统跟踪号:", "[" + liushui + "]");
//        return liushui;
//    }
//
//
//    //===========================三要素接口引用相关方法===============================
//
//    /**
//     * 解密密文，解密公安请求的报文
//     *
//     * @param reqBody 请求报文
//     * @return 解密后结果
//     */
//    private JSONObject doDec(JSONObject reqBody) {
//        // 具体的处理逻辑
//        // 1.使用我方秘钥对公安提供的基于我方公钥加密的密文数据C解密，【报文密文D】保存在Content字段。【秘钥密文C】保存在SignatureValue字段
//        // jyt私钥
//        // 解析请求报文-加密信息
//        log.info("开始解析gongAn请求数据：");
//        JSONObject body = reqBody.getJSONObject("Body");
//        if (body.isEmpty()) {
//            throw new RuntimeException("body解析失败，无相关信息");
//        }
//        // SM2加密后的密钥密文，取自请求报文SignatureValue，发送方密钥密文C
//        String cipherKeyC = body.getString("SignatureValue");
//        BodyModel bodyModel = new BodyModel();
//        bodyModel.setSignatureValue(cipherKeyC);
//        String cipherTextD = body.getString("Content");
//        bodyModel.setContent(cipherTextD);
//        //调用公共解密方法
//        String dataPlain = encryptAndDecryptTools.decryptData(bodyModel);
//        reqBody.put("dataAfterDec", dataPlain);
//        return reqBody;
//    }
//
//    /**
//     * 解密密文，解密公安请求的报文
//     *
//     * @param reqBody 请求报文
//     * @return 解密后结果
//     */
//    private JSONObject doDecBak(JSONObject reqBody) {
//        // 具体的处理逻辑
//        // 1.使用我方秘钥对公安提供的基于我方公钥加密的密文数据C解密，【报文密文D】保存在Content字段。【秘钥密文C】保存在SignatureValue字段
//        // jyt私钥
//        String privateKey = userCertPrivateKey;
//        // 解析请求报文-加密信息
//        log.info("开始解析gongAn请求数据：");
//        JSONObject body = reqBody.getJSONObject("Body");
//        if (body.isEmpty()) {
//            throw new RuntimeException("body解析失败，无相关信息");
//        }
//        // SM2加密后的密钥密文，取自请求报文SignatureValue，发送方密钥密文C
//        String cipherKeyC = body.getString("SignatureValue");
//        BodyModel bodyModel = new BodyModel();
//        bodyModel.setSignatureValue(cipherKeyC);
//        //String cipherKeyC = signatureValueStr;
//        // 2、使用秘钥明文F和SM4算法将报文密文D解密成明文
//        // SM4加密后的Base64报文，取自请求报文Content字段，整个Content都是加密后的报文，发送方报文密文D
//        String cipherTextD = body.getString("Content");
//        bodyModel.setContent(cipherTextD);
//        String dataPlain = encryptAndDecryptTools.decryptData(bodyModel);
//
//        //        // 使用SM2私钥对C进行解密，返回sm4明文数据F
//        //        byte[] sm4Byte = EncryptionUtils.sm2DecryptByte(privateKey, cipherKeyC);
//        //        // 转hex
//        //        String hexString = Hex.toHexString(sm4Byte);
//        //        log.info("SM2解密后的SM4明文F：{}", hexString);
//        //        // 2、使用秘钥明文F和SM4算法将报文密文D解密成明文
//        //        // SM4加密后的Base64报文，取自请求报文Content字段，整个Content都是加密后的报文，发送方报文密文D
//        //        String cipherTextD = body.getString("Content");
//        //        // 基于sm4对数据密文D进行解密，utf-8编码，得到报文明文
//        //        String dataPlainText = EncryptionUtils.sm4DecryptBankData(sm4Byte, cipherTextD);
//        //        log.info("SM4解密后的数据明文：{}", dataPlainText);
//        reqBody.put("dataAfterDec", dataPlain);
//        return reqBody;
//    }
//
//
//    //===========================卡开户接口引用相关方法===============================
//
//    /**
//     * 卡开户接口
//     * 组装发往分行前置的明文数据
//     * 查询卡开户信息
//     */
//    private JSONObject doPackageReqBodyToQz(String reqData, String reqSysSriNo) {
//        ObjectMapper objectMapper = new ObjectMapper();
//        JsonNode rootNode = null;
//        JSONObject resp = null;
//        try {
//            //解析总行明文
//            rootNode = objectMapper.readTree(reqData);
//            JsonNode txBody = rootNode.get("txBody");
//            JsonNode txEntity = txBody.get("txEntity");
//            String mediumNo = txEntity.get("mediumNo").asText();
//            Map<String, Object> body = new HashMap<>();
//            body.put("mediumNo", mediumNo);
//
//            Map<String, Object> txentity = new HashMap<>();
//            txentity.put("txEntity", body);
//
//            Map<String, Object> head = new HashMap<>();
//            JsonNode txHeader = rootNode.get("txHeader");
//            reqSysSriNo = txHeader.get("reqSysSriNo").asText();
//            // 流水号
//            head.put("reqSysSriNo", reqSysSriNo);
//            String startSysOrCmptNo = txHeader.get("startSysOrCmptNo").asText();
//            head.put("startSysOrCmptNo", startSysOrCmptNo);
//            String busiSendInstNo = txHeader.get("busiSendInstNo").asText();
//            head.put("busiSendInstNo", busiSendInstNo);
//            head.put("Content-Type", "application/json");
//
//            Map<String, Object> txbody = new HashMap<>();
//            txbody.put("txBody", txentity);
//
//
//            Map<String, Object> reqInfo = new HashMap<>();
//            reqInfo.put("txHead", head);
//            reqInfo.put("txBody", txbody);
//
//            // 将 Map 转换为 JSONObject对象
//            resp = new JSONObject(reqInfo);
//            return resp;
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
//
//    }
//
//    /**
//     * 加密报文并组装
//     * 请求分行前置，SM4  +  SM2
//     * 获得返回的报文信息
//     *
//     * @param reqBody 请求明文
//     * @param headers 请求头
//     */
//    private String doZonghangEnc(JSONObject reqBody, MultiValueMap<String, String> headers) throws Exception {
//        log.info("读取请求总行的明文报文...");
//
//        // 业务请求数据明文 ，返回加密后的请求报文(整体)
//        HttpHeaders requestHeaders = new HttpHeaders();
//        // 报文体字节长度有变动，content-length如果保持之前的值会导致请求异常，此处移除，请求组装的时候框架会重新计算
//        requestHeaders.remove("content-length");
//        // sysTrackNo 业务跟踪号
//        // sysTrackNo = "20250304100620135000101871921749";
//        // jyt: 303100300001 ，12位系统号；厦门分行前置：35310030000 ，11位系统号
//        String reqSysSriNo = getLsh("out", reqSysCode);
//        // 组装业务请求数据明文-查询卡开户
//        JSONObject requestBodyJson = doPackageReqBodyToQz(reqBody.toJSONString(), reqSysSriNo);
//        JSONObject qianzhiReq = psbcBranchSecurity10Service.psbcSecurity10Request(requestBodyJson, requestHeaders);
//        // Http请求分行前置接口-卡开户
//        String url = kakaihuUrl;
//        // 请求前置接口，直接获取返回信息
//        String response = HttpUtils.sendToQz(url, qianzhiReq.toString(), requestHeaders);
//        // 对返回结果进行解密
//        log.info("返回结果{}", response);
//        return response;
//    }
//
//
//
//
//
//
//    /**
//     * 加密密文发往公安-三要素
//     * * 加密流程
//     * * 1、对原文A进行UTF8编码，得到原文B；
//     * * 2、发起机构每次随机生成新SM4秘钥明文F，将报文加密后转成base64发送。
//     * * 3、使用【公钥证书】对自【秘钥明文F】进行SM2加密得到秘钥【秘钥密文C，格式为16进制的字符串（无需转大写）】。
//     * * 4、将【报文密文D】保存在Content字段。将【秘钥密文C】保存在SignatureValue字段。
//     *
//     * @param rspBody 响应给公安的请求报文
//     * @return 加密后结果
//     */
//    public JSONObject doEnc600386(JSONObject rspBody) {
//
//        // 1. 显式UTF-8编码处理（A转化成原文B）
//        byte[] utf8Bytes = rspBody.getBytes("UTF-8");
//        String plainTextB = new String(utf8Bytes, StandardCharsets.UTF_8);
//
//        // 2. 生成随机SM4密钥（秘钥明文F）
//        String sm4KeyF = EncryptionUtils.randSm4SecretKey();
//        byte[] sm4KeyBytes = DatatypeConverter.parseHexBinary(sm4KeyF);
//        // 打印密钥前20个字符
//        log.info("随机SM4密钥前20个字符:", sm4KeyF.substring(20));
//        //  使用F对报文进行SM4加密，得到报文密文G，转为base64格式的报文密文D
//        String cipherTextD = EncryptionUtils.sm4Encrypt(sm4KeyBytes, plainTextB);
//
//        // 3. 使用【公钥】E 对【秘钥明文F】进行SM2加密得到密钥16进制【秘钥密文C】, 银行提供的证书cer
//        String cerPublic = userCertPublicKey;
//        String cipherKeyC = EncryptionUtils.sm2EncryptByPublicKey(cerPublic, sm4KeyBytes);
//        cipherKeyC = cipherKeyC.toUpperCase();   // 强制转为大写十六进制
//
//        // 4、将【报文密文D】保存在Content字段。将【秘钥密文C】保存在SignatureValue字段。
//        BodyModel model = new BodyModel();
//        model.setContent(cipherTextD);
//        model.setSignatureValue(cipherKeyC);
//
//        // 转成json
//        JSONObject reqContent = JSONObject.parseObject(model.toString());
//        return reqContent;
//    }
//}
