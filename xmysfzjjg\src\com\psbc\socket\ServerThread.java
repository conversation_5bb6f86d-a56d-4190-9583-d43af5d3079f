package com.psbc.socket;

import com.alibaba.fastjson.JSON;
import com.psbc.util.ZhbgConfigUtil;
import com.psbc.webservice.ClientIWebService;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.Socket;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Iterator;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.frontend.ClientProxy;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.log4j.Logger;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

public class ServerThread extends Thread {
  private static final Logger logger = Logger.getLogger(ServerThread.class);
  
  //public static int length = 0;
  
  private Socket socket;
  
  private static final String CHINESE_CHARSET = "GBK";
  
  private static final int LEN_SIZE = 6;
  
  public ServerThread(Socket socket) {
    this.socket = socket;
  }
  
  private static String sendoutsys(String reqmsg) throws Exception {
    String rspmsg = null;
    ClassPathXmlApplicationContext context = new ClassPathXmlApplicationContext(new String[] { "client-beans.xml" });
    X509TrustManager tm = new X509TrustManager() {
        public X509Certificate[] getAcceptedIssuers() {
          return null;
        }
        
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {}
        
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {}
      };
    TLSClientParameters tls = new TLSClientParameters();
    ClientIWebService client = (ClientIWebService)context.getBean("client");
    Client proxy = ClientProxy.getClient(client);
    HTTPConduit conduit = (HTTPConduit)proxy.getConduit();
    tls.setSecureSocketProtocol("TLS");
    tls.setTrustManagers(new TrustManager[] { tm });
    tls.setDisableCNCheck(true);
    conduit.setTlsClientParameters(tls);
    rspmsg = client.Execute("YCYH", reqmsg);
    return rspmsg;
  }
  
  /**
   * 请求综合办公
   * 通过socket的tcp请求进来引用此方法
   * */
  private static String sendZhbg(String reqmsg,String serviceno) throws Exception {
	    String rspmsg = null;
	    String baseUrl = ZhbgConfigUtil.getvalue("zhbgPostUrl");
	    String zhbgurl = baseUrl;
	    String params = reqmsg;//传入接口的请求参数
	    if(serviceno.equals("30001")){
	    	logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>开始请求30001接口,传入json参数>>>>>>>>>>>>>>>>>>>>>>>>>>"+params.toString());
	    	zhbgurl = baseUrl+ZhbgConfigUtil.getvalue("url30001");
	    	logger.info(">>>>>>"+zhbgurl);
	    	OutputStreamWriter out = null;
	    	try {
	 	     URL url = null;	         
	 	     if(zhbgurl.contains("https:")){//这个后期还需要再优化一下
	 	    	 url  = new URL(null,zhbgurl,new sun.net.www.protocol.https.Handler());
	 	    	// HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
	 	     }else{
	 	    	 url = new URL(zhbgurl);//第三方接口路径
	 	     }
	 	     HttpURLConnection conn = (HttpURLConnection) url.openConnection();
	 	    		

	         // 创建连接
	         conn.setDoOutput(true);
	         conn.setDoInput(true);
	         conn.setUseCaches(false);
	         conn.setRequestMethod("POST");//请求方式 此处为POST
//	         String token= "123456789";//根据实际项目需要，可能需要token值
//	         conn.setRequestProperty("token", token);
	         conn.setRequestProperty("Content-type", "application/json");
	         conn.connect();
	         out = new OutputStreamWriter(conn.getOutputStream(), "utf-8");//编码设置
	         out.write(params);
	         out.flush();
	         out.close();
	         // 获取响应
	         BufferedReader reader = new BufferedReader( new InputStreamReader(conn.getInputStream()));
	         String lines;
	         StringBuffer sb = new StringBuffer();
	         while ((lines = reader.readLine()) != null ){
	             lines = new String(lines.getBytes(), "utf-8" );
	             sb.append(lines);
	         }
	         reader.close();
	         System.out.println("请求后返回的结果："+sb);
	         rspmsg = sb.toString();
	         logger.info(">>>>>>返回："+rspmsg);
	         logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>请求30001接口结束>>>>>>>>>>>>>>>>>>>>>>>>>>");
	    	}catch(Exception e){
	    		rspmsg = "请求30001异常："+e.getMessage();
	    		logger.error("请求30001异常："+e.getMessage());
	    	}
	    }if(serviceno.equals("20006")){
	    	logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>开始请求20006接口,传入xml参数>>>>>>>>>>>>>>>>>>>>>>>>>>");
	    	zhbgurl = baseUrl+ZhbgConfigUtil.getvalue("url20006");
	    	logger.info(">>>>>>"+zhbgurl);
	    	OutputStreamWriter out = null;
	    	try {
	 	    URL url = new URL(zhbgurl);//第三方接口路径
	         HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
	         // 创建连接
	         conn.setDoOutput(true);
	         conn.setDoInput(true);
	         conn.setUseCaches(false);
	         conn.setRequestMethod("POST");//请求方式 此处为POST
//	         String token= "123456789";//根据实际项目需要，可能需要token值
//	         conn.setRequestProperty("token", token);
	         conn.setRequestProperty("Content-type", "application/xml");
	         conn.connect();
	         //将发送的数据进行写入
	         out = new OutputStreamWriter(conn.getOutputStream(), "utf-8");//编码设置
	         out.write(params);
	         out.flush();
	         out.close();
	         // 获取响应，处理返回的数据
	         BufferedReader reader = new BufferedReader( new InputStreamReader(conn.getInputStream()));
	         String lines;
	         StringBuffer sb = new StringBuffer();
	         while ((lines = reader.readLine()) != null ){
	             lines = new String(lines.getBytes(), "utf-8" );
	             sb.append(lines);
	         }
	         reader.close();
	         System.out.println("请求后返回的结果："+sb);
	         rspmsg = sb.toString();
	         logger.info(">>>>>>返回："+rspmsg);
	         logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>请求20006接口结束>>>>>>>>>>>>>>>>>>>>>>>>>>");
	    	}catch(Exception e){
	    		rspmsg = "请求20006异常："+e.getMessage();
	    		logger.error("请求20006异常："+e.getMessage());
	    	}
	    }else{
	    	 logger.info("无操作，serviceno = "+serviceno);
	    }
   
        return rspmsg;        

	  }
  
  
  public void run() {
    OutputStream os = null;
    DataOutputStream dos = null;
    InputStream is = null;
    DataInputStream dis = null;
    String rpsmsg = null;
    try {
      logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>接收银行端报文开始>>>>>>>>>>>>>>>>>>>>>>>>>>");
    
      logger.info("银行端new commer:"+ this.socket.getRemoteSocketAddress().toString());
     
      
      os = this.socket.getOutputStream();
      dos = new DataOutputStream(os);
      is = this.socket.getInputStream();
      dis = new DataInputStream(is);
  
      
      byte[] msghead = new byte[6];
      dis.read(msghead);
      logger.info("银行端报文头:["+ new String(msghead)+"]");
      int len = Integer.parseInt((new String(msghead)).trim());
      logger.info("银行端报文长度:["+ len+"]");
      byte[] msgbody = new byte[len];
      int rlen = 0;
      while (rlen < len) {
    	if (rlen  < 0) {
          	break;
        }
        rlen += dis.read(msgbody, rlen, len - rlen);
        logger.info("读取报文长度rlen:"+ rlen);
        
      } 
     
      logger.info("银行端发送报文:["+ new String(msgbody,"UTF-8")+"]");
      //银行端发送的报文，需要判断30001和20006
      String msgJudge = new String(msgbody,"UTF-8");
      
      /////////////////////////////////////////////////////////////////////////////////////////////////////
       String text = ""; //关键字
      //判断是json还是xml
      Boolean a = isJson(msgJudge);
      logger.info("是否JSON？true为是，结果:"+a);
      Boolean b = isXML(msgJudge);
      logger.info("是否XML？true为是，结果:"+b);
      if(a){//是json
    	  logger.info("JSON请求获取serviceno-----");
			 HashMap map = JSON.parseObject(msgJudge,HashMap.class);
			 System.out.println("serviceno:"+map.get("serviceno").toString());
			 text = map.get("serviceno").toString();
      }else if(b){//是xml
    	  logger.info("XML请求获取serviceno-----");
			Document doc = DocumentHelper.parseText(msgJudge);
			Element root  = doc.getRootElement();
			System.out.println("根节点名称--"+root.getName());
			//获取根节点下的子节点head
			Iterator it = root.elementIterator("head");
			//遍历head节点
			while(it.hasNext()){
				Element recordEle = (Element) it.next();
				String serviceno = recordEle.elementTextTrim("serviceno");
				System.out.println("接口编码--"+serviceno);
				text = serviceno;
			}
      }
      
	
		/* 
		 if(text.equals("20006")){//调用综合办公20006接口 -xml
			 long start = System.nanoTime();
			 String rspxml = sendZhbg(msgJudge,"20006");
			 System.out.println("请求获取监管账户变动反馈信息："+rspxml);
			 long end = System.nanoTime();
		      logger.info("报文发往综合办公系统结束>>>");
		      logger.info("综合办公返回报文耗时:["+((end-start)/1000L/1000L)+"]秒");
		      logger.info("综合办公返回报文长度:["+(rspxml.getBytes("UTF-8")).length + "]");
		      rpsmsg = String.format("%06d", new Object[] { Integer.valueOf((rspxml.getBytes("UTF-8")).length) }) + rspxml;
		      logger.info("综合办公系统返回报文:["+rspxml +"]");
		      String msg = rpsmsg;
		      logger.info("综合办公系统返回报文发送银行端>>>");
		      dos.write(msg.getBytes("UTF-8"));
		      dos.flush();
		      logger.info("send finish.................");
		      logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>综合办公系统返回报文发送银行端結東>>>>>>>>>>>>>>>>>>>>>>>>>>");
		      dis.close();
		      dos.close();
		      this.socket.close();
		}else
		*/ 
		if(text.equals("30001")){//调用综合办公30001接口 -json,获取按揭贷款信息 
			 long start = System.nanoTime();
			 String rspjson = sendZhbg(msgJudge,"30001");
			 System.out.println("请求获取按揭贷款信息返回："+rspjson);
			 long end = System.nanoTime();
		      logger.info("报文发往综合办公系统结束>>>");
		      logger.info("综合办公返回报文耗时:["+((end-start)/1000L/1000L)+"]秒");
		      logger.info("综合办公返回报文长度:["+(rspjson.getBytes("UTF-8")).length + "]");
//		      rpsmsg = String.format("%06d", new Object[] { Integer.valueOf((rspjson.getBytes("UTF-8")).length) }) + rspjson;
		      logger.info("综合办公系统返回报文:["+rspjson +"]");
		      String msg = rspjson;
		      logger.info("综合办公系统返回报文发送银行端>>>");
		      dos.write(msg.getBytes("UTF-8"));
		      dos.flush();
		      logger.info("send finish.................");
		      logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>综合办公系统返回报文发送银行端結東>>>>>>>>>>>>>>>>>>>>>>>>>>");
		      dis.close();
		      dos.close();
		      this.socket.close();
		}else{//原来的逻辑，发往委托方
			  long start = System.nanoTime();
		      logger.info("报文发往委托方系统开始>>>");
		      	
  	          String rspxml = sendoutsys(new String(msgbody, "UTF-8"));
   //           String rspxml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<content >  <head>  <statecode>1</statecode> <msg>执行成功</msg>  </head>   <body> <serialno>XXX</serialno> <issuccess>1</issuccess> </body> </content>";
				
		      long end = System.nanoTime();
		      logger.info("报文发往委托方系统结束>>>");
		      logger.info("委托方系统返回报文耗时:["+((end-start)/1000L/1000L)+"]秒");
		      logger.info("委托方系统返回报文长度:["+(rspxml.getBytes("UTF-8")).length + "]");
		      rpsmsg = String.format("%06d", new Object[] { Integer.valueOf((rspxml.getBytes("UTF-8")).length) }) + rspxml;
		      logger.info("委托方系统返回报文:["+rspxml +"]");
		      String msg = rpsmsg;
		      logger.info("委托方系统返回报文发送银行端>>>");
		      dos.write(msg.getBytes("UTF-8"));
		      dos.flush();
		      logger.info("send finish.................");
		      logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>委托方系统返回报文发送银行端結東>>>>>>>>>>>>>>>>>>>>>>>>>>");
		      dis.close();
		      dos.close();
		      this.socket.close();
		}
   /////////////////////////////////////////////////////////////////////////////////////////////////////////////  	
     /* long start = System.nanoTime();
      logger.info("报文发往委托方系统开始>>>");
      String rspxml = sendoutsys(new String(msgbody, "UTF-8"));
      long end = System.nanoTime();
      logger.info("报文发往委托方系统结束>>>");
      logger.info("委托方系统返回报文耗时:["+((end-start)/1000L/1000L)+"]秒");
      logger.info("委托方系统返回报文长度:["+(rspxml.getBytes("UTF-8")).length + "]");
      rpsmsg = String.format("%06d", new Object[] { Integer.valueOf((rspxml.getBytes("UTF-8")).length) }) + rspxml;
      logger.info("委托方系统返回报文:["+rspxml +"]");
      String msg = rpsmsg;
      logger.info("委托方系统返回报文发送银行端>>>");
      dos.write(msg.getBytes("UTF-8"));
      dos.flush();
      logger.info("send finish.................");
      logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>委托方系统返回报文发送银行端結東>>>>>>>>>>>>>>>>>>>>>>>>>>");
      dis.close();
      dos.close();
      this.socket.close();*/
    } catch (Exception var24) {
      logger.error("SocketThread err:", var24);
    } finally {
      try {
        if (dis != null)
          dis.close(); 
        if (dos != null)
          dos.close(); 
        if (this.socket != null)
          this.socket.close(); 
      } catch (IOException var23) {
        var23.printStackTrace();
      } 
    } 
  }
  
  /**
	 * 判断是否是json结构
	 */
	public static boolean isJson(String value) {
		try {
			new JSONObject(value);
		} catch (JSONException e) {
			return false;
		}
		return true;
	}

	/**
	 * 判断是否是xml结构
	 */
	public static boolean isXML(String value) {
		try {
			DocumentHelper.parseText(value);
		} catch (DocumentException e) {
			return false;
		}
		return true;
	}
}
