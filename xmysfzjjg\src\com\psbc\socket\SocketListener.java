package com.psbc.socket;

import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.util.concurrent.Executors;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;

import com.psbc.httpApplication.MyHttpHandler;
import com.psbc.util.ConfigUtil;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;

public class SocketListener implements ServletContextListener {
  private static final Logger logger = Logger.getLogger(SocketListener.class);
  
  private PayServerSocket payServerSocket = null;
  
  public void contextDestroyed(ServletContextEvent arg0) {
	logger.info("停止对中间业务socket服务端监听");
    if (this.payServerSocket != null)
      this.payServerSocket.stopserver(); 
  }
  
  public void contextInitialized(ServletContextEvent arg0) {
	//启动应用 
    if (this.payServerSocket == null) {
      logger.info("开启对中间业务socket服务端监听");
      this.payServerSocket = new PayServerSocket();
      this.payServerSocket.start();
    } 
    logger.info("开启httpServelet");
    //startHttpService();
  }
  
  /**
   * 启动http服务 - 此服务暂时不用
   * */
  private static void startHttpService()  {
	  String port = ConfigUtil.getvalue("HTTP_PORT");
	  try{
		  
		  HttpServer server = HttpServer.create(new InetSocketAddress(Integer.parseInt(port)), 0);
	     //   server.createContext("/api/admin/xzp/queryYxjfLsxdye", new MyHandler());
	        server.createContext("/api/admin/xzp/queryYxjfLsxdye", new MyHttpHandler());
	        server.setExecutor(Executors.newFixedThreadPool(10)); // null-使用默认执行器
	        server.start();
	        logger.info("启动httpServer成功,端口："+port);
	  }catch(Exception e){
		  System.out.println(e.getMessage());
	  }
      
  }
  
  static class MyHandler implements HttpHandler {


    @Override
      public void handle(HttpExchange t) throws IOException {
    	// 获取请求参数
          String query = t.getRequestURI().getQuery();
          String response = "Query parameters: " + query;
          // 定义响应内容
         // String response = "This is the response";
          System.out.println(response);
          t.sendResponseHeaders(200, response.length()); // 设置响应头
          OutputStream os = t.getResponseBody(); // 获取输出流
          os.write(response.getBytes()); // 将响应内容写入输出流
          os.close(); // 关闭输出流
      }
  }
  
  static class MyHandler2 implements HttpHandler {
      @Override
      public void handle(HttpExchange t) throws IOException {
          // 定义响应内容
         // String response = "This is the response2";
       // 获取请求参数
          String query = t.getRequestBody().toString();
          String response = "Query parameters2: " + query;
          System.out.println("2--"+t.getHttpContext());
          t.sendResponseHeaders(200, response.length()); // 设置响应头
          OutputStream os = t.getResponseBody(); // 获取输出流
          os.write(response.getBytes()); // 将响应内容写入输出流
          os.close(); // 关闭输出流
      }
  }
}
