apiVersion: v1
kind: Service
metadata:
  labels:
    app: istio-ingressgateway
    install.operator.istio.io/owning-resource-namespace: istio-system
    istio: ingressgateway
    istio.io/rev: default
    operator.istio.io/component: IngressGateways
    operator.istio.io/managed: Reconcile
    release: istio
  name: istio-ingressgateway-lb-biz
  namespace: istio-system
spec:
  ipFamilyPolicy: SingleStack
  ports:
    - name: branch-agent
      port: 6666
      protocol: TCP
      targetPort: 6666
  selector:
    app: istio-ingressgateway
    istio: ingressgateway
  type: LoadBalancer
