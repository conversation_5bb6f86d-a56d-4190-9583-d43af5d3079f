//package com.psbc.cpufp.util;
//
//import cn.hutool.crypto.Mode;
//import cn.hutool.crypto.Padding;
//import cn.hutool.crypto.asymmetric.KeyType;
//import cn.hutool.crypto.asymmetric.SM2;
//import cn.hutool.crypto.symmetric.SM4;
//import lombok.extern.slf4j.Slf4j;
//import org.bouncycastle.jce.provider.BouncyCastleProvider;
//import org.bouncycastle.util.encoders.Hex;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.xml.bind.DatatypeConverter;
//import java.io.FileInputStream;
//import java.io.FileNotFoundException;
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//import java.security.MessageDigest;
//import java.security.NoSuchProviderException;
//import java.security.SecureRandom;
//import java.security.Security;
//import java.security.cert.Certificate;
//import java.security.cert.CertificateException;
//import java.security.cert.CertificateFactory;
//import java.security.interfaces.ECPublicKey;
//
///**
// * 加解密工具类
// * */
//@Slf4j
//@Service
//public class EncryptionUtils {
//    private static final Logger log = LoggerFactory.getLogger(EncryptionUtils.class);
//
//    // SM2非对称加密
//    /**
//     * 基于银行提供的公钥字符串对数据进行加密，提供密文给银行
//     *
//     * @param publicKey 银行公钥
//     * @param plainText 字符串
//     * @return  sm4密文
//     * @throws Exception 异常信息
//     */
//    public static String sm2EncryptByPublicKey(String publicKey,   String plainText) {
//        Security.addProvider(new BouncyCastleProvider());
//        SM2 sm2 = new SM2(null,   publicKey);
//        String result = sm2.encryptHex(plainText,   KeyType.PublicKey);
//        log.info("SM4密钥密文（SM2公钥加密的）：",   result);
//        return result;
//    }
//
//    // SM2非对称加密
//    /**
//     * 基于银行提供的公钥字符串对数据进行加密，提供密文给银行
//     *
//     * @param publicKey 公钥
//     * @param sm4SecreteKey sm4密钥
//     * @return  sm4密文
//     * @throws Exception 异常信息
//     */
//    public static String sm2EncryptByPublicKey(String publicKey,   byte[] sm4SecreteKey) {
//        Security.addProvider(new BouncyCastleProvider());
//        SM2 sm2 = new SM2(null,   publicKey);
//        String result = sm2.encryptHex(sm4SecreteKey,   KeyType.PublicKey);
//        log.info("SM4密钥密文（SM2公钥加密的）：",  result);
//        return result;
//    }
//
//    // SM2非对称加密
//    /**
//     * 基于银行提供的cer证书对数据进行加密，提供密文给银行
//     *
//     * @param cerFile 证书文件
//     * @param sm4SecreteKey sm4密钥
//     * @return  sm4密文
//     * @throws Exception 异常信息
//     */
//    public static String sm2EncryptByCerFile(String cerFile,   byte[] sm4SecreteKey) {
//        Security.addProvider(new BouncyCastleProvider());
//        String result = "";
//        FileInputStream fileInputStream = null;
//        try {
//            // 加载证书文件
//            fileInputStream = new FileInputStream(cerFile);
//            CertificateFactory cf = CertificateFactory.getInstance("X.509",   "BC");
//            Certificate certificate = cf.generateCertificate(fileInputStream);
//            // 获取SM2公钥
//            ECPublicKey publicBankKey = (ECPublicKey) certificate.getPublicKey();
//            SM2 sm2 = new SM2(null,   publicBankKey);
//            result = sm2.encryptHex(sm4SecreteKey,   KeyType.PublicKey).toUpperCase();
//            log.info("SM4密钥密文（SM2公钥加密的）：{}",  result);
//            return result;
//        } catch (FileNotFoundException e) {
//            log.error("证书文件cer获取异常：{}",  e.getMessage());
//            throw new RuntimeException("证书文件cer获取异常!");
//        } catch (CertificateException e) {
//            log.error("证书文件cer解析异常：{}",  e.getMessage());
//            throw new RuntimeException("证书文件cer解析异常!");
//        } catch (NoSuchProviderException e) {
//            log.error("证书文件cer解析失败，不支持特定的安全提供程序：{}", e.getMessage());
//            throw new RuntimeException("证书文件cer解析失败，不支持特定的安全提供程序!");
//        } finally {
//            if (fileInputStream != null) {
//                try {
//                    fileInputStream.close();
//                } catch (IOException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        }
//    }
//
//    /**
//     * 使用我方秘钥对银行提供的基于我方公钥加密的密文数据解密
//     *
//     * @param privateKey 私钥
//     * @param cipherText 密文数据
//     * @return sm4明文
//     */
//    public static byte[] sm2DecryptByte(String privateKey, String cipherText) {
//        Security.addProvider(new BouncyCastleProvider());
//        SM2 sm2 = new SM2(privateKey, null);
//        return sm2.decrypt(cipherText, KeyType.PrivateKey);
//    }
//
//
//    /**
//     * 使用我方秘钥对银行提供的基于我方公钥加密的密文数据解密
//     *
//     *
//     * @param privateKey  我方密钥
//     * @param cipherText  密文
//     * @return sm4明文
//     */
//    public static String sm2DecryptToString(String privateKey,   String cipherText)  {
//        byte[] result = sm2DecryptByte(privateKey, cipherText);
//        String resStr = "";
//        try {
//            resStr = new String(result, "UTF-8");
//        } catch (Exception e) {
//            log.error("使用我方秘钥对银行提供的基于我方公钥加密的密文数据解密报错:{}", e.getMessage());
//        }
//        return resStr;
//    }
//
//    /**
//     * 动态生成随机的SM4公钥
//     *
//     * @return 返回
//     */
//    public static String randSm4SecretKey() {
//        SecureRandom random = new SecureRandom();
//        // 128位 = 16字节
//        byte[] randomBytes = new byte[16];
//        random.nextBytes(randomBytes);
//        // 将随机数转换为十六进制字符串表示
//        StringBuilder sb = new StringBuilder();
//        for (byte b : randomBytes) {
//            sb.append(String.format("%02X",   b));
//        }
//        // SM4-规则定制为大写
//        log.info("SM4明文数据：",  sb.toString());
//        return sb.toString();
//    }
//
//    // SM4对称加密
//    /**
//     * SM4对称加密
//     * */
//    public static String sm4Encrypt(byte[] secretKey,   String plainText) {
//        SM4 sm4 = new SM4(Mode.ECB,   Padding.PKCS5Padding,   secretKey);
//        String result = sm4.encryptBase64(plainText,   StandardCharsets.UTF_8);
//        log.info("SM4加密后的数据密文:",  result);
//        // 加密后转base64
//        return result;
//    }
//
//    // SM4对称解密
//    /**
//     * SM4对称解密
//     * */
//    public static String sm4DecryptStr(String secretKey,   String cipherText) {
//        byte[] secretByte = Hex.decode(secretKey);
//        SM4 sm4 = new SM4(Mode.ECB,   Padding.PKCS5Padding,   secretByte);
//        String result = sm4.decryptStr(cipherText);
//        log.info("SM4解密后的数据明文:",  result);
//        return result;
//    }
//
//    /**
//     *
//     * */
//    public static String sm4DecryptBankData(byte[] secretKey,   String cipherText) {
//        SM4 sm4 = new SM4(Mode.ECB,   Padding.PKCS5Padding,   secretKey);
//        return sm4.decryptStr(cipherText);
//    }
//
//    /**
//     * 对我方数据进行加密提供给银行Sm4WithSm2
//     *
//     * @param cerFilePath 证书路径
//     * @param jsonPlainText 加密串
//     * @return 返回bodyModel
//     */
//    public static BodyModel encryptMyDataToBank(String cerFilePath,   String jsonPlainText) {
//        // 1. 生成随机sm4的secretKey
//        String sm4Key = randSm4SecretKey();
//        byte[] sm4Bytes = DatatypeConverter.parseHexBinary(sm4Key);
//        // 2. 基于银行的公钥对sm4进行sm2加密
//        String sm4Cipher = sm2EncryptByCerFile(cerFilePath,   sm4Bytes);
//        // 3. 基于sm4对数据报文进行sm4加密
//        String dataCipher = sm4Encrypt(sm4Bytes,   jsonPlainText);
//        BodyModel model = new BodyModel();
//        model.setContent(dataCipher);
//        model.setSignatureValue(sm4Cipher);
//        return model;
//    }
//
//    /**
//     * 对我方数据进行加密提供给银行Sm4WithSm2  通过sm2公钥
//     *
//     * @param publicKey 公钥
//     * @param jsonPlainText 加密数据
//      * @return 返回BodyModel
//     */
//    public static BodyModel encryptMyDataToBankByPublicKey(String publicKey,   String jsonPlainText) {
//        // 1. 生成随机sm4的secretKey
//        String sm4Key = randSm4SecretKey();
//        byte[] sm4Bytes = DatatypeConverter.parseHexBinary(sm4Key);
//        // 2. 基于银行的公钥对sm4进行sm2加密
//        String sm4Cipher = sm2EncryptByPublicKey(publicKey,   sm4Bytes);
//        // 3. 基于sm4对数据报文进行sm4加密
//        String dataCipher = sm4Encrypt(sm4Bytes,   jsonPlainText);
//        BodyModel model = new BodyModel();
//        model.setContent(dataCipher);
//        model.setSignatureValue(sm4Cipher);
//        return model;
//    }
//
//    /**
//     * 对银行提供过来的数据进行解密sm2WithSm4
//     *
//     * @param privateKey 密钥
//     * @param sm4Cipher sm4密文
//     * @param dataCipher 加密数据
//     * @return 返回字符串
//     */
//    public static String decryptBankData(String privateKey,   String sm4Cipher,   String dataCipher) {
//        // 1. 使用我方sm2私钥对sm4密文进行解密
//        // 银行sm4密文需手动在前面补04
//        byte[] sm4Byte = sm2DecryptByte(privateKey, sm4Cipher);
//        // 转hex
//        String sm4PlainText = Hex.toHexString(sm4Byte);
//        log.info("SM2解密后的SM4明文：",  sm4PlainText);
//
//        // 2. 基于sm4对数据密文进行解密
//        String dataPlainText = sm4DecryptBankData(sm4Byte,   dataCipher);
//        log.info("SM4解密后的数据明文:",  dataPlainText);
//        return dataPlainText;
//    }
//
//    /**
//     *
//     * */
//    public static String decryptBankData(String privateKey,   BodyModel model) {
//        String sm4Cipher = model.getSignatureValue();
//        String dataCipher = model.getContent();
//        return decryptBankData(privateKey,   sm4Cipher,   dataCipher);
//    }
//
//    //    public static String decryptBankFileData(String privateKey,   FileAttachment model) {
//    //        String sm4Cipher = model.getSignatureValue();
//    //        String dataCipher = model.getContent();
//    //        return decryptBankData(privateKey,   sm4Cipher,   dataCipher);
//    //    }
//
//    /**
//     *测试方法
//     * */
//    public static void main(String[] args)  {
//        String publi = "";
//        String priva = "";
//
//        //        //建设银行public
//        //        publi = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEDtEp2urTKvxO8VL2P4iEJFc6FrMf, pm6DaijHsqquyMZgP4Q7wdKcR361vTI76bWDzHSivRVxCVDCBBtlZj6cQ==";
//        publi = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAENWHYBmioTIPpAZFdccHqwgCDG2nTRtea/VkhPUOeBB725, IvI9rsKclWkObCRC1dadxYoF, wL, RXQrpVkWmnMA==";
//        priva = "";
//        String myString = "这是我的信息加密";
//        String enString = sm2EncryptByPublicKey(publi,   myString);
//        log.info("加密后：{}", enString);
//        String deString = sm2DecryptToString(priva, enString);
//        log.info("解密后：{}", deString);
//    }
//
//    /**
//     *
//     * @param utf8Str utf8字符串
//     * @return 返回byte[]
//     */
//    private static byte[] strHash256(String utf8Str) {
//        byte[] res = null;
//        try {
//            MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
//            res = sha256.digest(utf8Str.getBytes("UTF-8"));
//            return res;
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
//        return res;
//    }
//
//}
