package com.psbc.xmysfzjjg.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.nio.charset.StandardCharsets;

/**
 * 改进的Socket客户端测试工具
 * 正确处理6字节长度+消息体的格式
 */
public class ImprovedSocketClientTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ImprovedSocketClientTest.class);
    
    public static void main(String[] args) {
        testSocketConnection();
    }
    
    public static void testSocketConnection() {
        String host = "127.0.0.1";
        int port = 8888;
        
        logger.info("开始测试Socket连接: {}:{}", host, port);
        
        // 测试JSON消息
        testMessage(host, port, "{\"serviceno\":\"30001\",\"data\":\"test json message\"}", "JSON");
        
        // 等待一下
        try { Thread.sleep(1000); } catch (InterruptedException e) { /* ignore */ }
        
        // 测试XML消息
        testMessage(host, port, 
                "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>20006</serviceno></head>" +
                "<body><data>test xml message</data></body></content>", "XML");
        
        // 等待一下
        try { Thread.sleep(1000); } catch (InterruptedException e) { /* ignore */ }
        
        // 测试普通消息（发往委托方）
        testMessage(host, port, 
                "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>other</serviceno></head>" +
                "<body><data>test normal message</data></body></content>", "Normal");
    }
    
    private static void testMessage(String host, int port, String message, String type) {
        Socket socket = null;
        OutputStream os = null;
        InputStream is = null;
        
        try {
            logger.info("=== 测试{}消息 ===", type);
            logger.info("消息内容: {}", message);
            
            socket = new Socket(host, port);
            os = socket.getOutputStream();
            is = socket.getInputStream();
            
            // 构造请求：6位长度 + 消息内容
            byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
            String lengthStr = String.format("%06d", messageBytes.length);
            
            logger.info("发送长度: {}", lengthStr);
            
            // 发送长度字段
            os.write(lengthStr.getBytes(StandardCharsets.UTF_8));
            // 发送消息体
            os.write(messageBytes);
            os.flush();
            
            logger.info("消息已发送，等待响应...");
            
            // 读取响应长度（6字节）
            byte[] lengthBuffer = new byte[6];
            int totalRead = 0;
            while (totalRead < 6) {
                int bytesRead = is.read(lengthBuffer, totalRead, 6 - totalRead);
                if (bytesRead == -1) {
                    throw new IOException("连接已关闭");
                }
                totalRead += bytesRead;
            }
            
            String responseLengthStr = new String(lengthBuffer, StandardCharsets.UTF_8);
            logger.info("响应长度字符串: '{}'", responseLengthStr);
            
            int responseLength = Integer.parseInt(responseLengthStr.trim());
            logger.info("响应长度: {}", responseLength);
            
            // 读取响应体
            byte[] responseBuffer = new byte[responseLength];
            totalRead = 0;
            while (totalRead < responseLength) {
                int bytesRead = is.read(responseBuffer, totalRead, responseLength - totalRead);
                if (bytesRead == -1) {
                    throw new IOException("连接已关闭");
                }
                totalRead += bytesRead;
            }
            
            String response = new String(responseBuffer, StandardCharsets.UTF_8);
            logger.info("收到{}响应: {}", type, response);
            logger.info("=== {}消息测试完成 ===", type);
            
        } catch (Exception e) {
            logger.error("测试{}消息时发生异常", type, e);
        } finally {
            closeQuietly(is);
            closeQuietly(os);
            closeQuietly(socket);
        }
    }
    
    private static void closeQuietly(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                // 忽略关闭异常
            }
        }
    }
    
    private static void closeQuietly(Socket socket) {
        if (socket != null) {
            try {
                socket.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
        }
    }
}
