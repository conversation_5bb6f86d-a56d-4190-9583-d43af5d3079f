package com.psbc.xmysfzjjg;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;

/**
 * 简单的Socket测试
 */
public class SimpleSocketTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleSocketTest.class);
    
    public static void main(String[] args) {
        testSimpleConnection();
    }
    
    public static void testSimpleConnection() {
        String host = "127.0.0.1";
        int port = 8888;
        
        try (Socket socket = new Socket(host, port);
             OutputStream os = socket.getOutputStream();
             InputStream is = socket.getInputStream()) {
            
            logger.info("成功连接到 {}:{}", host, port);
            
            // 发送一个简单的JSON消息
            String message = "{\"serviceno\":\"30001\",\"test\":\"hello\"}";
            byte[] messageBytes = message.getBytes("UTF-8");
            String lengthStr = String.format("%06d", messageBytes.length);
            String fullMessage = lengthStr + message;
            
            logger.info("发送消息: {}", fullMessage);
            os.write(fullMessage.getBytes("UTF-8"));
            os.flush();
            
            // 等待响应
            Thread.sleep(1000);
            
            // 读取响应
            if (is.available() > 0) {
                byte[] buffer = new byte[1024];
                int bytesRead = is.read(buffer);
                String response = new String(buffer, 0, bytesRead, "UTF-8");
                logger.info("收到响应: {}", response);
            } else {
                logger.info("没有收到响应数据");
            }
            
        } catch (Exception e) {
            logger.error("连接测试失败", e);
        }
    }
}
