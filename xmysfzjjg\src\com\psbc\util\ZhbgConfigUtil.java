package com.psbc.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

/**
 * 读取综合办公配置文件
 * */
public class ZhbgConfigUtil {
  private static final Logger logger = LogManager.getLogger(ZhbgConfigUtil.class);
  
  private static String filename = "zhbgConf.properties";
  
  private static Properties props = new Properties();
  
  static {
    try {
      ClassLoader loader = ZhbgConfigUtil.class.getClassLoader();
      InputStream ips = loader.getResourceAsStream(filename);
      BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(ips, "UTF-8"));
      props.load(bufferedReader);
    } catch (IOException var3) {
    	logger.error("加载文件["+ filename +"]失败!", var3);
    } catch (NullPointerException var4) {
    	logger.error("配置文件["+filename + "]不存在!", var4);
    } 
    logger.error("加载配置文件完成!");
  }
  
  public static String getvalue(String key) {
    return props.getProperty(key);
  }
  
  public static void main(String[] args) {
    System.out.println(getvalue("abc"));
  }
}
