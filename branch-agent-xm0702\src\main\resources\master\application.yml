server:
  port: 6667

mybatis:
  # 配置mapper文件的位置
  mapper-locations: classpath*:mapper/*.xml
  # 配置类型别名包
  type-aliase-package: com.psbc.cpufp

#必填
#响应码的定位信息，通常为系统号前7位，，按照行内响应码标准，响应码通常为:一级分类+ 7位定位信息 + 二级分类 + 6位序列号，位于common-response-enum依赖
enum_config:
  prefix: 1350001

#监控暴露端点
management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health"

logging:
  config: classpath:dev/logback-spring.xml

spring:
  application:
    name: branch-agent-base-demo
#已启动的kernel的grpc注册地址，如127.0.0.1:20081
soma:
  registryUrl: soma-kernel.cpufp-plat:20081
  #本地内嵌服务启动端口，须保证该端口未被其他进程占用
  remotePort: 9944
  #执行器校验令牌
  execAccessToken: ${soma_token}
  #执行器集群id
  execGroupId: ${soma_id}
  #该配置项须保证与执行器集群的注册方式一致
  isAutoRegistry: 1
  #日志适配器路径(可选，如果不配置日志打印方式默认为System.out.println)
  loggerAdapter: com.psbc.cpufp.soma.logger.SomaLogger
  #执行调度任务的最大线程数(可选)
  #  remoteMaxThreadNum: 3
  #当前执行器的版本 要与创建的执行器版本一致
  execVersion: default