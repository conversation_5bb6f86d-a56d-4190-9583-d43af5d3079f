package com.psbc.xmysfzjjg.util;

import com.psbc.xmysfzjjg.config.XmysfzjjgProperties;
import com.psbc.xmysfzjjg.config.ZhbgProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 配置服务类
 * 替代原有的ConfigUtil和ZhbgConfigUtil
 */
@Service
public class ConfigService {
    
    @Autowired
    private XmysfzjjgProperties xmysfzjjgProperties;
    
    @Autowired
    private ZhbgProperties zhbgProperties;
    
    /**
     * 获取xmysfzjjg配置值
     */
    public String getXmysfzjjgValue(String key) {
        switch (key) {
            case "IP":
                return xmysfzjjgProperties.getIp();
            case "PORT":
                return String.valueOf(xmysfzjjgProperties.getPort());
            case "WG_PORT":
                return String.valueOf(xmysfzjjgProperties.getWgPort());
            case "HTTP_PORT":
                return String.valueOf(xmysfzjjgProperties.getHttpPort());
            case "MESSAGE":
                return xmysfzjjgProperties.getMessage();
            default:
                return null;
        }
    }
    
    /**
     * 获取综合办公配置值
     */
    public String getZhbgValue(String key) {
        switch (key) {
            case "zhbgPostUrl":
                return zhbgProperties.getPostUrl();
            case "url30001":
                return zhbgProperties.getUrl30001();
            case "url20006":
                return zhbgProperties.getUrl20006();
            case "reqSysNo":
                return zhbgProperties.getReqSysNo();
            case "zhbgPublicKey":
                return zhbgProperties.getZhbgPublicKey();
            case "selfPublicKey":
                return zhbgProperties.getSelfPublicKey();
            case "privateKey":
                return zhbgProperties.getPrivateKey();
            default:
                return null;
        }
    }
    
    // 便捷方法
    public XmysfzjjgProperties getXmysfzjjgProperties() {
        return xmysfzjjgProperties;
    }
    
    public ZhbgProperties getZhbgProperties() {
        return zhbgProperties;
    }
}
