# XMYSFZJJG 迁移故障排查指南

## 常见问题及解决方案

### 1. 应用启动失败

#### 问题：端口被占用
```
Port 6666 was already in use
```
**解决方案：**
- 检查端口占用：`netstat -ano | findstr :6666`
- 杀死占用进程或更改端口配置

#### 问题：Netty启动失败
```
Failed to bind to port 8888
```
**解决方案：**
- 检查Socket端口占用：`netstat -ano | findstr :8888`
- 修改application.yml中的netty.socket-port配置

#### 问题：配置文件错误
```
Could not resolve placeholder
```
**解决方案：**
- 检查application.yml语法是否正确
- 确保环境变量已设置（如soma_token, soma_id）

### 2. Socket连接问题

#### 问题：连接被拒绝
```
Connection refused
```
**解决方案：**
1. 确认Netty服务器已启动
2. 检查日志中是否有Netty启动成功的消息
3. 验证端口配置是否正确

#### 问题：SocketMessageHandler错误
```
is not a @Sharable handler
```
**解决方案：**
- 已修复：在SocketMessageHandler类上添加了@ChannelHandler.Sharable注解

#### 问题：消息格式错误
```
无法解析消息长度
```
**解决方案：**
- 确保客户端发送的消息格式为：6位长度 + 消息体
- 检查字符编码是否为UTF-8

### 3. WebService问题

#### 问题：WSDL无法访问
```
404 Not Found
```
**解决方案：**
- 检查CXF配置是否正确
- 访问：http://localhost:6666/wservices/IWebServiceService?wsdl
- 查看启动日志中CXF相关信息

#### 问题：WebService调用失败
```
Message part was not recognized
```
**解决方案：**
- 检查WSDL定义是否正确
- 确认客户端调用参数格式

### 4. 综合办公接口问题

#### 问题：网络连接超时
```
Connection timeout
```
**解决方案：**
- 检查综合办公系统网络连通性
- 验证zhbg.post-url配置是否正确
- 检查防火墙设置

#### 问题：SSL证书错误
```
SSL handshake failed
```
**解决方案：**
- 已配置信任所有证书
- 如仍有问题，检查SSL配置

### 5. 配置问题

#### 问题：配置未生效
```
Property not found
```
**解决方案：**
- 确认@ConfigurationProperties类已被扫描
- 检查@EnableConfigurationProperties配置
- 验证配置前缀是否正确

#### 问题：多环境配置混乱
```
Wrong profile active
```
**解决方案：**
- 明确指定profile：`--spring.profiles.active=local`
- 检查application-{profile}.yml文件是否存在

## 调试技巧

### 1. 启用调试日志
在application.yml中添加：
```yaml
logging:
  level:
    com.psbc.xmysfzjjg: DEBUG
    io.netty: DEBUG
```

### 2. 检查应用状态
```bash
# 健康检查
curl http://localhost:6666/test/health

# 配置检查
curl http://localhost:6666/test/config

# Actuator健康检查
curl http://localhost:6666/actuator/health
```

### 3. 网络连接测试
```bash
# 测试Socket端口
telnet localhost 8888

# 测试HTTP端口
curl http://localhost:6666/test/health
```

### 4. 日志分析
查看关键日志：
- Netty启动：`Netty Socket服务器启动成功`
- WebService发布：`Setting the server's publish address`
- 应用启动完成：`Started BranchAgentApplication`

## 性能优化建议

### 1. Netty配置优化
```yaml
xmysfzjjg:
  netty:
    worker-threads: 20  # 根据CPU核心数调整
    connect-timeout: 60 # 根据网络情况调整
```

### 2. JVM参数优化
```bash
java -Xmx2g -Xms2g -XX:+UseG1GC -jar branch-agent.jar
```

### 3. 连接池配置
如需要，可配置HTTP连接池：
```yaml
spring:
  http:
    client:
      connection-timeout: 5000
      read-timeout: 30000
```

## 回滚方案

如果迁移后出现严重问题，可以：

1. **保留原项目备份**
2. **使用原有部署方式**
3. **逐步迁移功能**

## 联系支持

如遇到无法解决的问题：
1. 收集完整的错误日志
2. 记录重现步骤
3. 提供环境信息（JDK版本、操作系统等）

## 验证清单

迁移完成后，请验证以下功能：

- [ ] 应用正常启动
- [ ] Netty Socket服务可连接
- [ ] WebService WSDL可访问
- [ ] REST API响应正常
- [ ] 综合办公接口调用成功
- [ ] 委托方系统转发正常
- [ ] 配置文件加载正确
- [ ] 日志输出正常

使用验证脚本：
```bash
# Windows
verify-migration.bat

# Linux/Mac
chmod +x verify-migration.sh
./verify-migration.sh
```
