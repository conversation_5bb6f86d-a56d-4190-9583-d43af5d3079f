package com.psbc.xmysfzjjg.netty;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * xmysfzjjg消息解码器
 * 处理前6字节长度 + 消息体的格式
 */
public class XmysfzjjgMessageDecoder extends ByteToMessageDecoder {
    
    private static final Logger logger = LoggerFactory.getLogger(XmysfzjjgMessageDecoder.class);
    private static final int LENGTH_FIELD_SIZE = 6;
    
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 检查是否有足够的字节读取长度字段
        if (in.readableBytes() < LENGTH_FIELD_SIZE) {
            return;
        }
        
        // 标记当前读取位置
        in.markReaderIndex();
        
        // 读取长度字段（6字节）
        byte[] lengthBytes = new byte[LENGTH_FIELD_SIZE];
        in.readBytes(lengthBytes);
        
        String lengthStr = new String(lengthBytes).trim();
        int messageLength;
        
        try {
            messageLength = Integer.parseInt(lengthStr);
        } catch (NumberFormatException e) {
            logger.error("无法解析消息长度: {}", lengthStr);
            // 重置读取位置并跳过这个字节
            in.resetReaderIndex();
            in.readByte();
            return;
        }
        
        // 检查是否有足够的字节读取完整消息
        if (in.readableBytes() < messageLength) {
            // 重置读取位置，等待更多数据
            in.resetReaderIndex();
            return;
        }
        
        // 读取消息体
        byte[] messageBytes = new byte[messageLength];
        in.readBytes(messageBytes);
        
        String message = new String(messageBytes, "UTF-8");
        logger.debug("解码消息: 长度={}, 内容={}", messageLength, message);
        
        out.add(message);
    }
}
