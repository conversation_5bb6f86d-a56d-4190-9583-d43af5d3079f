@echo off
echo ========================================
echo XMYSFZJJG 最终验证测试
echo ========================================

echo.
echo 检查应用状态...
curl -s http://localhost:6666/test/health > nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 应用正在运行
) else (
    echo ✗ 应用未运行！
    echo.
    echo 请先重启应用以使修复生效：
    echo 1. 停止当前应用 (Ctrl+C)
    echo 2. 重新启动: mvn spring-boot:run -Dspring.profiles.active=local
    echo 3. 等待启动完成后再运行此脚本
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 1. 测试基础功能
echo ========================================

echo.
echo 测试健康检查...
curl -s http://localhost:6666/test/health
echo.

echo.
echo 测试配置加载...
curl -s http://localhost:6666/test/config-service
echo.

echo.
echo ========================================
echo 2. 测试Socket服务 (改进版)
echo ========================================

echo.
echo 运行改进的Socket客户端测试...
java -cp "target/classes;target/test-classes" com.psbc.xmysfzjjg.client.ImprovedSocketClientTest

echo.
echo ========================================
echo 3. 测试Socket服务 (原版修复)
echo ========================================

echo.
echo 运行修复后的原Socket客户端测试...
java -cp "target/classes;target/test-classes" com.psbc.xmysfzjjg.client.SocketClientTest

echo.
echo ========================================
echo 4. 测试WebService
echo ========================================

echo.
echo 检查WebService WSDL...
curl -s http://localhost:6666/wservices/IWebServiceService?wsdl | findstr "wsdl:definitions" > nul 2>&1
if %errorlevel% == 0 (
    echo ✓ WebService WSDL 可访问
    echo   地址: http://localhost:6666/wservices/IWebServiceService?wsdl
) else (
    echo ✗ WebService WSDL 不可访问
)

echo.
echo ========================================
echo 5. 测试REST API
echo ========================================

echo.
echo 测试综合办公30001接口...
curl -X POST -H "Content-Type: application/json" -d "{\"serviceno\":\"30001\",\"test\":\"data\"}" http://localhost:6666/api/admin/xzp/queryYxjfLsxdye
echo.

echo.
echo 测试综合办公20006接口...
curl -X POST -H "Content-Type: application/xml" -d "<?xml version=\"1.0\"?><content><head><serviceno>20006</serviceno></head><body><test>data</test></body></content>" http://localhost:6666/api/admin/xzp/queryJgzhInfo
echo.

echo.
echo ========================================
echo 测试完成！
echo ========================================

echo.
echo 如果所有测试都通过，说明迁移完全成功！
echo.
echo 服务端点总结：
echo - Socket服务: localhost:8888
echo - HTTP服务: http://localhost:6666
echo - WebService: http://localhost:6666/wservices/IWebServiceService?wsdl
echo - REST API: http://localhost:6666/api/admin/xzp/
echo - 健康检查: http://localhost:6666/test/health
echo.
echo 如果有任何问题，请查看：
echo - 应用日志: logs/app-branch-agent-base-demo-*.log
echo - 故障排查: TROUBLESHOOTING.md
echo - 修复记录: FIXES_APPLIED.md
echo.
pause
