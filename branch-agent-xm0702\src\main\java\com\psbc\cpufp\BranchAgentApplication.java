package com.psbc.cpufp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

/**
 * <AUTHOR>
 * 集成xmysfzjjg项目的SpringBoot应用
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.psbc.cpufp", "com.psbc.xmysfzjjg", "org.soma.job"})
@EnableFeignClients
@EnableConfigurationProperties
public class BranchAgentApplication {
    public static void main(String[] args) {
        SpringApplication.run(BranchAgentApplication.class, args);
        System.out.println("===========================================");
        System.out.println("Branch Agent with xmysfzjjg 启动成功!");
        System.out.println("WebService地址: http://localhost:6666/wservices/IWebServiceService?wsdl");
        System.out.println("HTTP接口地址: http://localhost:6666/api/admin/xzp/");
        System.out.println("Socket服务端口: 8888");
        System.out.println("===========================================");
    }
}
