//package com.psbc.cpufp.util;
//
////import com.alibaba.fastjson.JSONObject;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.http.HttpEntity;
//import org.apache.http.ParseException;
//import org.apache.http.client.config.RequestConfig;
//import org.apache.http.client.methods.CloseableHttpResponse;
//import org.apache.http.client.methods.HttpPost;
//import org.apache.http.entity.StringEntity;
//import org.apache.http.impl.client.CloseableHttpClient;
//import org.apache.http.impl.client.HttpClients;
//import org.apache.http.util.EntityUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.http.HttpHeaders;
//import org.springframework.stereotype.Service;
//
//import java.io.IOException;
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.Random;
//
///**
// * 发送http请求工具类
// * */
//@Slf4j
//@Service
//public final class HttpUtils {
//
//    private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);
//
//    /**
//     * 发送http请求往分行前置
//     *
//     * @param uri 请求路径
//     * @param content 请求参数
//     */
//    public static String sendToQz(String uri, String content, HttpHeaders requestHeaders) throws Exception {
//        //        if (uri.startsWith("https://")) {
//        //            return sendHttps(uri, type, content);
//        //        }
//        String message = "";
//        CloseableHttpClient httpClient = null;
//        try {
//            httpClient = HttpClients.createDefault();
//            RequestConfig config = RequestConfig.custom()
//                    .setSocketTimeout(60000)
//                    .setConnectTimeout(10000)
//                    .setConnectionRequestTimeout(20000)
//                    .build();
//            HttpPost httpPost = new HttpPost(uri);
//            httpPost.setConfig(config);
//            requestHeaders.forEach((key, values) -> {
//                System.out.println("请求前遍历Header: " + key);
//                values.forEach(value -> System.out.println("  Value: " + value));
//            });
//            //从content获取com_headers ，通用头
//            httpPost.setHeader("Content-Type",  "application/json"); //协议格式
//            Object reqSysCode = requestHeaders.get("reqSysCode");
//            if (reqSysCode != null) {
//                httpPost.setHeader("reqSysCode", java.net.URLEncoder.encode(reqSysCode.toString(),"UTF-8")); //接入系统代码
//            }
//            Object userCertSn = requestHeaders.get("userCertSn");
//            if (userCertSn != null) {
//                httpPost.setHeader("userCertSn", java.net.URLEncoder.encode(userCertSn.toString(),"UTF-8")); //使用证书 测试环境：0188ffa5d564 生产环境：018f0f093391
//            }
//            httpPost.setHeader("securityLevel", "10"); //安全等级10
//            Object txTime = requestHeaders.get("txTime");
//            if (txTime != null) {
//                httpPost.setHeader("txTime", java.net.URLEncoder.encode(txTime.toString(),"UTF-8")); //时间戳+17位
//            }
//            Object sysTrackNo = requestHeaders.get("sysTrackNo");
//            if (sysTrackNo != null) {
//                httpPost.setHeader("sysTrackNo", java.net.URLEncoder.encode(sysTrackNo.toString(),"UTF-8")); //任务通知号
//            }
//            Object sign = requestHeaders.get("sign");
//            if (sign != null) {
//                httpPost.setHeader("sign", java.net.URLEncoder.encode(sign.toString(),"UTF-8")); //签名
//            }
//            httpPost.setEntity(new StringEntity(content, "UTF-8"));
//            log.info("请求报文1 {}", (Object) httpPost.getAllHeaders());
//            log.info("请求报文：{} ", httpPost.getEntity());
//
//            @SuppressWarnings("VariableDeclarationUsageDistance")
//            CloseableHttpResponse response = httpClient.execute(httpPost);
//            log.info("响应报文：{}", response);
//            log.info("响应报文头 ：{}", (Object) response.getAllHeaders());
//            log.info("委托方请求状态：{}", response.getStatusLine().getStatusCode());
//            if (response.getStatusLine().getStatusCode() == 200) {
//                HttpEntity recEntity = response.getEntity();
//                message = EntityUtils.toString(recEntity, "UTF-8");
//            }
//            response.close();
//            httpClient.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//            log.error("请求失败");
//            log.error(e.toString());
//        } catch (ParseException e) {
//            e.printStackTrace();
//            log.error("请求失败");
//            log.error(e.toString());
//        } finally {
//            if (httpClient != null) {
//                httpClient.close();
//            }
//            log.info("请求执行结束");
//        }
//        //   //log.info("message={}", message);
//        return message;
//    }
//
//    //    /**
//    //     * 发送https请求
//    //     *
//    //     * @param url 请求路径
//    //     * @param type 请求数据格式
//    //     * @param data 请求参数
//    //     */
//    //    public static String sendHttps(String url, String type, String data) {
//    //        String message = "";
//    //        //创建post请求对象
//    //        HttpPost httpPost = new HttpPost(url);
//    //        //创建CloseableHttpClient对象（忽略证书的重点）
//    //        CloseableHttpClient client = null;
//    //        try {
//    //            SSLConnectionSocketFactory scsf = new SSLConnectionSocketFactory(
//    //                    SSLContexts.custom().loadTrustMaterial(null, new TrustSelfSignedStrategy()).build(),
//    //                    NoopHostnameVerifier.INSTANCE);
//    //            if (client != null){
//    //                client = HttpClients.custom()
//    //                        .setSSLSocketFactory(scsf)
//    //                        .build();
//    //            }
//    //
//    //        } catch (KeyManagementException e) {
//    //            e.printStackTrace();
//    //        } catch (NoSuchAlgorithmException e) {
//    //            e.printStackTrace();
//    //        } catch (KeyStoreException e) {
//    //            e.printStackTrace();
//    //        }
//    //        try {
//    //            RequestConfig config = RequestConfig.custom()
//    //                    .setSocketTimeout(60000)
//    //                    .setConnectTimeout(10000)
//    //                    .setConnectionRequestTimeout(20000)
//    //                    .build();
//    //            httpPost.setConfig(config);
//    //            //设置请求头
//    //            httpPost.setEntity(new StringEntity(data, Charset.forName("utf-8")));
//    //            httpPost.setHeader("Content-Type", type);
//    //            //log.info("请求内容：{}", data);
//    //            //使用CloseableHttpClient发送请求
//    //            CloseableHttpResponse response = client.execute(httpPost);
//    //            //获取返回code
//    //            int statusCode = response.getStatusLine().getStatusCode();
//    //            //log.info("委托方请求状态：{}", statusCode);
//    //            //根据返回code进行处理
//    //            if (statusCode == 200) {
//    //                //获取响应结果
//    //                HttpEntity entity = response.getEntity();
//    //                message = EntityUtils.toString(entity, Charset.forName("UTF-8"));
//    //            }
//    //            response.close();
//    //            client.close();
//    //        } catch (IOException e) {
//    //            //log.info("请求失败");
//    //            //log.info(e.getLocalizedMessage());
//    //            e.printStackTrace();
//    //        }
//    //        return message;
//    //    }
//
//    /**
//     * 获取流水号
//     *
//     * @return 返回流水号
//     */
//    public static String getLsh() {
//        int kk = new Random().nextInt(999999);
//        //String liushui = UUID.randomUUID().toString().replaceAll("-", "");
//        String liushui = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()) + String.format("%06d", kk);
//        //log.info("拼接成的流水号为:" + "[" + liushui + "]");
//        return liushui;
//    }
//
//    /**
//     * 测试
//     * */
//    public static void main(String[] args) throws Exception {
//        //System.out.println(getLsh());
//        //System.out.println("aaa|+|".split("\\|\\+\\|", -1)[1]);
//        //        String encode = URLEncoder.encode("1 +23汉字", "UTF-8");
//        //        System.out.println(encode);
//        //        String decode = URLDecoder.decode(encode, "UTF-8");
//        //        System.out.println(decode);
//        //        if (true) {
//        //            return;
//        //        }
//        //
//        //        String httpsUrl = "https://127.0.0.1:30147/psbc/orderQuery";
//        //        String json = "{\n"
//        //                +  "    \"applicationId\": \"huaianjiapei\", \n"
//        //                +  "    \"code\": \"3435\", \n"
//        //                +  "    \"stuName\": \"李丹丹\", \n"
//        //                +  "    \"stuMobile\": \"15366642267\", \n"
//        //                + "    \"orderStatus\": \"1\", \n"
//        //                +  "    \"stuCertNo\": \"320811199912253026\"\n"
//        //                +  "}";
//        //    System.out.println(send(httpsUrl, "application/json; charset=utf-8", json));
//
//    }
//}
