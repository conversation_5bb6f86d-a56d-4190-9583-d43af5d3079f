package com.psbc.httpApplication;

import com.psbc.socket.ServerThread;
import com.psbc.util.ZhbgConfigUtil;
import com.sun.net.httpserver.Headers;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.net.ssl.HttpsURLConnection;

import org.apache.log4j.Logger;
import cn.hutool.http.HttpRequest;

/**

* 处理/myserver路径请求的处理器类
* http请求

*/

public class MyHttpHandler implements HttpHandler {
	private static final Logger logger = Logger.getLogger(HttpHandler.class);

	@Override
	
	public void handle(HttpExchange httpExchange) {
		System.out.print("--------------------进入请求综合办公获取按揭贷款信息接口------------------------------------------");
	
		try {
		
			StringBuilder responseText = new StringBuilder();
			responseText.append(getRequestParam(httpExchange));
			
//			responseText.append("请求方法：").append(httpExchange.getRequestMethod()).append("");	
//			responseText.append("请求参数：").append(getRequestParam(httpExchange)).append("");		
//			responseText.append("请求头：").append(getRequestHeader(httpExchange));
//			logger.info("请求接口的相关信息："+responseText.toString());
			String reqMsg = responseText.toString();
			
			logger.info("请求综合办公参数："+reqMsg);
			//请求综合办公
			//String resp = sendZhbg(reqMsg,"30001");	
			String url = ZhbgConfigUtil.getvalue("zhbgPostUrl")+ZhbgConfigUtil.getvalue("url30001");
			String resp = send(url,reqMsg);
			handleResponse(httpExchange, resp);
		
		} catch (Exception ex) {		
			ex.printStackTrace();		
		}
	
	}
	
		/**
		 * http发送post请求
		 * 目前用于向综合办公系统发送请求
		 * */
	   public static String send(String url,String jsonStr){
		    logger.info("转发到："+url);
		    logger.info("传入参数："+jsonStr);
		    String resp = "";
		    try{
		    	resp = HttpRequest.post(url)
		                .header("Content-Type", "application/json")
		                .body(jsonStr)
		                .execute().body();
		        logger.info("请求响应："+resp);
		    }catch(Exception e){
		    	 logger.info("请求响应异常："+e.getMessage());
		    	 resp = e.getMessage();
		    }
	        
	        return resp;
	    }
	
	   /**
	    * 往综合办公系统发送请求并获取响应值
	    * 该方法暂时不用
	    * */
	  private static String sendZhbg(String reqmsg,String serviceno) throws Exception {
		    String rspmsg = null;
		    String baseUrl = ZhbgConfigUtil.getvalue("zhbgPostUrl");
		    String zhbgurl = baseUrl;
		    String params = reqmsg;//传入接口的请求参数
		    if(serviceno.equals("30001")){
		    	logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>开始请求30001接口,传入json参数>>>>>>>>>>>>>>>>>>>>>>>>>>"+params.toString());
		    	zhbgurl = baseUrl+ZhbgConfigUtil.getvalue("url30001");
		    	logger.info(">>>>>>"+zhbgurl);
		    	OutputStreamWriter out = null;
		    	try {
		 	     URL url = null;	         
		 	     if(zhbgurl.contains("https:")){//这个后期还需要再优化一下
		 	    	 url  = new URL(null,zhbgurl,new sun.net.www.protocol.https.Handler());
		 	    	// HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
		 	     }else{
		 	    	 url = new URL(zhbgurl);//第三方接口路径
		 	     }
		 	     HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		 	    		

		         // 创建连接
		         conn.setDoOutput(true);
		         conn.setDoInput(true);
		         conn.setUseCaches(false);
		         conn.setRequestMethod("POST");//请求方式 此处为POST
//		         String token= "123456789";//根据实际项目需要，可能需要token值
//		         conn.setRequestProperty("token", token);
		         conn.setRequestProperty("Content-type", "application/json");
		         conn.connect();
		         out = new OutputStreamWriter(conn.getOutputStream(), "utf-8");//编码设置
		         out.write(params);
		         out.flush();
		         out.close();
		         // 获取响应
		         BufferedReader reader = new BufferedReader( new InputStreamReader(conn.getInputStream()));
		         String lines;
		         StringBuffer sb = new StringBuffer();
		         while ((lines = reader.readLine()) != null ){
		             lines = new String(lines.getBytes(), "utf-8" );
		             sb.append(lines);
		         }
		         reader.close();
		         System.out.println("请求后返回的结果："+sb);
		         rspmsg = sb.toString();
		         logger.info(">>>>>>返回："+rspmsg);
		         logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>请求30001接口结束>>>>>>>>>>>>>>>>>>>>>>>>>>");
		    	}catch(Exception e){
		    		rspmsg = "请求30001异常："+e.getMessage();
		    		logger.error("请求30001异常："+e.getMessage());
		    	}
		    }else if(serviceno.equals("20006")){//查询监管账户信息
		    	logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>开始请求20006接口,传入xml参数>>>>>>>>>>>>>>>>>>>>>>>>>>");
		    	zhbgurl = baseUrl+ZhbgConfigUtil.getvalue("url20006");
		    	logger.info(">>>>>>"+zhbgurl);
		    	OutputStreamWriter out = null;
		    	try {
		 	    URL url = new URL(zhbgurl);//第三方接口路径
		         HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
		         // 创建连接
		         conn.setDoOutput(true);
		         conn.setDoInput(true);
		         conn.setUseCaches(false);
		         conn.setRequestMethod("POST");//请求方式 此处为POST
//		         String token= "123456789";//根据实际项目需要，可能需要token值
//		         conn.setRequestProperty("token", token);
		         conn.setRequestProperty("Content-type", "application/xml");
		         conn.connect();
		         //将发送的数据进行写入
		         out = new OutputStreamWriter(conn.getOutputStream(), "utf-8");//编码设置
		         out.write(params);
		         out.flush();
		         out.close();
		         // 获取响应，处理返回的数据
		         BufferedReader reader = new BufferedReader( new InputStreamReader(conn.getInputStream()));
		         String lines;
		         StringBuffer sb = new StringBuffer();
		         while ((lines = reader.readLine()) != null ){
		             lines = new String(lines.getBytes(), "utf-8" );
		             sb.append(lines);
		         }
		         reader.close();
		         System.out.println("请求后返回的结果："+sb);
		         rspmsg = sb.toString();
		         logger.info(">>>>>>返回："+rspmsg);
		         logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>请求20006接口结束>>>>>>>>>>>>>>>>>>>>>>>>>>");
		    	}catch(Exception e){
		    		rspmsg = "请求20006异常："+e.getMessage();
		    		logger.error("请求20006异常："+e.getMessage());
		    	}
		    }
	   
	        return rspmsg;        

		  }

/**

* 获取请求头

* @param httpExchange

* @return

*/

private String getRequestHeader(HttpExchange httpExchange) {

	Headers headers = httpExchange.getRequestHeaders();
	String head = "";
	return head;
	
//	return headers.entrySet().stream()
//			.map((Map.Entry> entry) -> entry.getKey() + ":" + entry.getValue().toString())
//			.collect(Collectors.joining(""));

}

/**

* 获取请求参数

* @param httpExchange

* @return

* @throws Exception

*/

private String getRequestParam(HttpExchange httpExchange) throws Exception {

	String paramStr = "";
	
	if (httpExchange.getRequestMethod().equals("GET")) {
	
		//GET请求读queryString
		
		paramStr = httpExchange.getRequestURI().getQuery();
	
	} else {
	
		//非GET请求读请求体
		
		BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(httpExchange.getRequestBody(), "utf-8"));
		
		StringBuilder requestBodyContent = new StringBuilder();
		
		String line = null;
		
		while ((line = bufferedReader.readLine()) != null) {
		
		requestBodyContent.append(line);
		
		}
		
		paramStr = requestBodyContent.toString();
	
	}

	return paramStr;

}
	
	/**
	
	* 处理响应
	
	* @param httpExchange
	
	* @param responsetext
	
	* @throws Exception
	
	*/
	
	private void handleResponse(HttpExchange httpExchange, String responsetext) throws Exception {

		//生成html
		
		StringBuilder responseContent = new StringBuilder();
		
		responseContent.append("")
		.append("")
		.append(responsetext)
		.append("")
		.append("");
		
		String responseContentStr = responseContent.toString();
		
		byte[] responseContentByte = responseContentStr.getBytes("utf-8");
		
		//设置响应头，必须在sendResponseHeaders方法之前设置！
		
		httpExchange.getResponseHeaders().add("Content-Type:", "text/html;charset=utf-8");
		
		//设置响应码和响应体长度，必须在getResponseBody方法之前调用！
		
		httpExchange.sendResponseHeaders(200, responseContentByte.length);
		
		OutputStream out = httpExchange.getResponseBody();
		
		out.write(responseContentByte);
		
		out.flush();
		
		out.close();

	}

}

