package com.psbc.xmysfzjjg.listener;

import com.psbc.xmysfzjjg.netty.NettySocketServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 应用启动监听器
 * 替代原有的SocketListener
 */
@Component
public class ApplicationStartupListener {
    
    private static final Logger logger = LoggerFactory.getLogger(ApplicationStartupListener.class);
    
    @Autowired
    private NettySocketServer nettySocketServer;
    
    /**
     * 应用启动完成后的处理
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logger.info("应用启动完成，开始初始化xmysfzjjg服务...");
        
        try {
            // Netty服务器已经通过@PostConstruct自动启动
            logger.info("xmysfzjjg服务初始化完成");
            
        } catch (Exception e) {
            logger.error("xmysfzjjg服务初始化失败", e);
        }
    }
}
