//package com.psbc.cpufp.soma.job;
//
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.soma.job.client.SomaJob;
//import org.soma.job.client.core.grpc.rpc.SomaHandler;
//import org.soma.job.client.model.JobParamVO;
//import org.springframework.stereotype.Service;
//
///**
// * 业务模板定时任务示例
// * handlerName:表示定时任务的名称，需要要soma管理端任务名配置保持一致
// *
// * <AUTHOR>
// */
//@Slf4j
//@Service
//@SomaHandler(handlerName = "BranchTestJobHandler")
//public class BranchTestJobHandler implements SomaJob {
//    private static final Logger log = LoggerFactory.getLogger(BranchTestJobHandler.class);
//
//    /**
//     * 定时任务执行
//     * */
//    @Override
//    public String execute(JobParamVO jobParam) {
//        log.info("定时任务执行");
//        // 业务逻辑执行
//        return "BranchTestJobHandler 定时任务被执行了";
//    }
//}
