package com.psbc.cpufp;

import com.psbc.cpufp.common.encryptor.SM4Encryptor;

/**
 * <AUTHOR>
 */
public class PasswordEncTest {
    public static void main(String[] args) throws Exception{
        // 生成加密密钥
        String password = SM4Encryptor.generateKey();
        // 使用生成的密钥加密明文
        String encrypt = SM4Encryptor.encrypt(password, "12345");
        // 打印密钥及密文
        System.out.println("password:"+password);
        System.out.println("encrypt:ENC("+encrypt+")");
    }
}
