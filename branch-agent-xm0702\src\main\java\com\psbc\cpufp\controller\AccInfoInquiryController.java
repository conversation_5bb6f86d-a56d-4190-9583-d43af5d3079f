//package com.psbc.cpufp.controller;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.psbc.cpufp.entity.jyt.JytInterLog;
//// import com.psbc.cpufp.sdk.security.SignTool;
//// import com.psbc.cpufp.sdk.security.DecryptTool;
//// import com.psbc.cpufp.sdk.security.VerifyTool;
//// import com.psbc.cpufp.sdk.security.GenerateSecretKeyTool;
//// import com.psbc.cpufp.sdk.security.EncryptTool;
//import com.psbc.cpufp.service.impl.AccInfoInquiryServiceImpl;
//import com.psbc.cpufp.service.impl.JytInterLogServiceImpl;
//import com.psbc.cpufp.util.BodyModel;
//import com.psbc.cpufp.util.EncryptAndDecryptTools;
//import lombok.extern.slf4j.Slf4j;
////import org.bouncycastle.util.encoders.Hex;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
////import org.springframework.http.ResponseEntity;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 账户信息查询接口
// * 公安请求JYT, JTY请求分行前置卡开户信息
// */
//@RestController
//@Slf4j
//public class AccInfoInquiryController {
//
//    private static final Logger log = LoggerFactory.getLogger(AccInfoInquiryController.class);
//
//    @Resource
//    private AccInfoInquiryServiceImpl aciServiceImpl;
//
//    @Resource
//    private EncryptAndDecryptTools encryptAndDecryptTools;
//
//    @Resource
//    private JytInterLogServiceImpl jytInterLogService;
//
//
//    /**
//     * 跳跃被接入透传接口，南接入
//     * 公安请求账户信息查询接口，传入请求报文
//     *
//     * @param reqBody 请求报文
//     * @param headers 请求header
//     * @return 响应结果
//     */
//    @PostMapping("/accInfoInquiry")
//    public JSONObject accInfoInquiry(@RequestBody JSONObject reqBody, @RequestHeader MultiValueMap<String, String> headers) {
//        log.info("进入/accInfoInquiry接口，请求参数reqBody:{}, headers:{}", reqBody.toJSONString(), headers.toString());
//        // 记录日志
//        JytInterLog jytInterLog = new JytInterLog();
//        jytInterLog.setRequester("GA");
//        jytInterLog.setResponder("JYT");
//        jytInterLog.setReqInter("jyt账户信息查询接口");
//        jytInterLog.setReqInterUrl("/accInfoInquiry");
//        jytInterLog.setReqParam("请求参数：" + reqBody.toJSONString());
//        jytInterLog.setAttrAddr("");
//        jytInterLog.setOthMsg1("公安发起请求查询账户信息");
//        // 0-成功，1-失败
//        String reqRes = "1";
//        String reqDesc = "进入接口还未开始请求";
//        try {
//            // 公安请求报文加密-测试
//            //-----------测试加解密-----------
//            //            String reqBody1 = reqBody.get("Body").toString();
//            //            BodyModel bodyModel1 = encryptAndDecryptTools.encryptMyDataToBankByPublicKey(reqBody1);
//
//            BodyModel bodyModel2 = new BodyModel();
//            JSONObject o = (JSONObject) JSON.toJSON(reqBody.get("Body"));
//            Object content = o.get("Content");
//            if (content != null) {
//                bodyModel2.setContent(content.toString());
//            } else {
//                log.error("Content空");
//            }
//            Object signatureValue = o.get("SignatureValue");
//            if (signatureValue != null) {
//                bodyModel2.setSignatureValue(signatureValue.toString());
//            } else {
//                log.error("SignatureValue空");
//            }
//            String s = encryptAndDecryptTools.decryptData(bodyModel2);
//            System.out.println("解密后的公安请求报文，body部分:" + s);
//            //组装完整的报文
//            Map<String, Object> gaBody = new HashMap<>();
//            gaBody.put("Body", s);
//
//            JSONObject reqHead = (JSONObject) JSON.toJSON(reqBody.get("Head"));
//            Map<String, Object> gaHead = new HashMap<>();
//            gaHead.put("Head", reqHead);
//
//            Map<String, Object> respMap = new HashMap<>();
//            respMap.put("Head", gaHead);
//            respMap.put("Body", gaBody);
//            JSONObject remoteRequestBody = new JSONObject(respMap);
//
//            //-----------测试加解密-----------
//            // 公安请求报文（解密）
//            //JSONObject remoteRequestBody = doDec(reqBody);
//            // remoteRequestBody - 解密后的公安请求报文 , 请求分行前置接口-卡开户信息（对应总行接口601087）-加密报文，组装并发送请求给分行前置，获取返回报文
//            String responseEntityQianzhi = aciServiceImpl.doZonghangEnc(remoteRequestBody, headers);
//            // 解析总行响应（解密）明文
//            JSONObject zhRespInfo = aciServiceImpl.doZonhangDec(responseEntityQianzhi);
//            // 将总行结果处理成发往公安的合规报文，明文
//            JSONObject toGongAn = doChangeToGongAn(zhRespInfo, "accInfoInquiry");
//            // 向公安请求的报文，响应给公安的报文（加密）
//            JSONObject resultResBody = aciServiceImpl.doEnc(toGongAn);
//            // 将结果响应给公安
//            // 响应结果http请求发往北接出端口9901
//            reqRes = "0";
//            reqDesc = "账户信息查询成功";
//            return resultResBody;
//        } catch (Exception e) {
//            log.error("EncryptMessage failed | Error: {}", e.getMessage());
//            reqRes = "1";
//            reqDesc = "接口请求异常：" + e.getCause();
//            throw new RuntimeException("Encryption process failed", e);
//        } finally {
//            jytInterLog.setRespRes(reqRes);
//            jytInterLog.setRespDesc(reqDesc);
//            log.info(jytInterLog.toString());
//            boolean logFlag = jytInterLogService.insertLog(jytInterLog);
//            if (logFlag) {
//                log.info("成功记录日志，{}", jytInterLog.toString());
//            } else {
//                log.error("记录日志失败, 在控制台打印：{}", jytInterLog.toString());
//            }
//        }
//
//    }
//
//    /**
//     *拼接报文 发往公安 -卡开户
//     *
//     * @param responseInfo 前置卡开户接口返回的密文报文信息
//     *
//     */
//    private JSONObject doChangeToGongAn(JSONObject responseInfo, String type) {
//        JSONObject respToGa = null;
//        if (type.equals("accInfoInquiry")) {
//            log.info("开始处理前置响应报文...");
//            JSONObject head = responseInfo.getJSONObject("txHead");
//            String servRespCd = head.get("servRespCd").toString();
//            String servRespDescInfo = head.get("servRespDescInfo").toString();
//            log.info("servRespCd:{},servRespDescInfo:{}", servRespCd, servRespDescInfo);
//            JSONObject body = responseInfo.getJSONObject("txBody");
//            JSONObject entity = body.getJSONObject("txEntity");
//            // 客户名称
//            String custNm = entity.getString("custNm");
//            log.info("custNm:{}", custNm);
//            // 个人证件号码
//            String personalCertNo = entity.getString("personalCertNo");
//            log.info("personalCertNo:{}", personalCertNo);
//            // 介质编号
//            String mediumNo = entity.getString("mediumNo");
//            log.info("mediumNo:{}", mediumNo);
//            // 介质类型代码
//            String mediumTpCd = entity.getString("mediumTpCd");
//            log.info("mediumTpCd:{}", mediumTpCd);
//            // 账户余额
//            String accBal = entity.getString("accBal");
//            log.info("accBal:{}", accBal);
//            // 开户日期
//            String openaccDate = entity.getString("openaccDate");
//            log.info("openaccDate:{}", openaccDate);
//            // 开户机构名称
//            String openAccInstName = entity.getString("openAccInstName");
//            log.info("openAccInstName:{}", openAccInstName);
//            // 开户机构号
//            String openAccInstNo = entity.getString("openAccInstNo");
//            log.info("openAccInstNo:{}", openAccInstNo);
//            // 个人存款账户状态标志码
//            String persDepAccStaFlagCd = entity.getString("persDepAccStaFlagCd");
//            log.info("persDepAccStaFlagCd:{}", persDepAccStaFlagCd);
//            log.info("开始转换成公安响应报文明文...");
//            Map<String, Object> gaHead = new HashMap<>();
//            gaHead.put("TxCode", "0305"); // 报文类型编码
//            gaHead.put("Code", servRespCd); // 响应码
//            gaHead.put("Description", servRespDescInfo); // 响应状态
//            gaHead.put("data", ""); // 返回结果数据
//            gaHead.put("TransSerialNumber", ""); // 交易跟踪码
//            gaHead.put("From", ""); // 发送机构ID
//            gaHead.put("To", "");  // 接收机构ID
//
//            Map<String, Object> gaContent = new HashMap<>();
//            // 客户名称
//            gaContent.put("AccountSubjectName", custNm);
//            // 证件号码
//            gaContent.put("TransactionRemark", personalCertNo);
//            //"账号"
//            gaContent.put("AccountNumber", mediumNo);
//            //"账户类型"
//            gaContent.put("RiskCode", mediumTpCd);
//            //"账户余额"
//            gaContent.put("AvailableBalance", accBal);
//            //"开户日期"
//            gaContent.put("AccountOpenTime", openaccDate);
//            //"开户银行"
//            gaContent.put("BankName", openAccInstName);
//            //"开户网点"
//            gaContent.put("DepositBankBranch", openAccInstNo);
//            //"账户状态"
//            gaContent.put("AccountStatus", persDepAccStaFlagCd);
//            gaContent.put("ExtendedFields", "扩展字段");
//            gaContent.put("ExtendedFields1", "扩展字段1");
//            gaContent.put("ExtendedFields2", "扩展字段2");
//            gaContent.put("ExtendedFields3", "扩展字段3");
//            gaContent.put("ExtendedFields4", "扩展字段4");
//            gaContent.put("Remarks", "备注");
//
//            Map<String, Object> gaAttachments = new HashMap<>();
//            // 文件名,参考附录O.1
//            gaAttachments.put("Filename", null);
//            // 文件加密base64码
//            gaAttachments.put("Content", null);
//            // 数字信封密文
//            gaAttachments.put("SignatureValue", null);
//            // 算法名称
//            gaAttachments.put("SignatureMethod", null);
//
//            Map<String, Object> gaBody = new HashMap<>();
//            // 目前明文，要转成base64编码
//            gaBody.put("Content", gaContent);
//            // 数字信封密文
//            gaBody.put("SignatureValue", "");
//            gaBody.put("Attachments", gaAttachments);
//
//            Map<String, Object> respMap = new HashMap<>();
//            respMap.put("Head", gaHead);
//            respMap.put("Body", gaBody);
//            respToGa = new JSONObject(respMap);
//            return respToGa;
//        }
//        return respToGa;
//    }
//}
