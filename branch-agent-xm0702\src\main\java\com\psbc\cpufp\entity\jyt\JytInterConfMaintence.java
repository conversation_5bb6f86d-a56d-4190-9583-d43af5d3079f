//package com.psbc.cpufp.entity.jyt;
//
//
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.experimental.Accessors;
//
//import java.io.Serializable;
//import java.util.List;
//
///**
// * <AUTHOR> Ye
// * @version 1.0.0
// * @title JytInterConfMaintence
// * @description 警银通_接口配置表
// * @create 2025/4/14 18:26
// **/
//@Data
//@Setter
//@Getter
//@Accessors(chain = true)
//@EqualsAndHashCode(callSuper = false)
//public class JytInterConfMaintence implements Serializable {
//    private static final long serialVersionUID = 1L;
//    /**
//     * 主键id
//     */
//    private String id;
//
//
//    /**
//     * 报文顺序
//     */
//    private String orderNum;
//    /**
//     * 接口服务ID
//     */
//    private String serviced;
//    /**
//     * 接口服务名称
//     */
//    private String servicedName;
//    /**
//     * 报文类型
//     */
//    private String msgType;
//    /**
//     * 节点级别（0-根节点；1-1级节点；2-2级节点）
//     */
//    private String msgNodeLevel;
//    /**
//     * 上级节点标识（0-根节点，其他为字段标志，比如Body）
//     */
//    private String msgUpNode;
//    /**
//     * 是否有下级节点：0-否，1-是
//     */
//    private String ifhaveChildNode;
//    /**
//     * 节点名称
//     */
//    private String msgNodeName;
//    /**
//     * 节点标识
//     */
//    private String msgNodeCode;
//    /**
//     * 节点类型
//     */
//    private String nodeType;
//    /**
//     * 是否必填
//     */
//    private String requiredOrNot;
//    /**
//     * 备注
//     */
//    private String remark;
//    /**
//     * 是否有效
//     */
//    private String effectiveOrNot;
//    /**
//     * 节点值
//     */
//    private String nodeValue;
//
//
//    private List<JytInterConfMaintence> children;
//
//}
