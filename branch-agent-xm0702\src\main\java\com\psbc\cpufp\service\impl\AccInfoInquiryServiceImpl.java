//package com.psbc.cpufp.service.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.JsonNode;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.psbc.cpufp.service.AccInfoInquireInterface;
//import com.psbc.cpufp.util.EncryptionUtils;
//import com.psbc.cpufp.util.BodyModel;
//import com.psbc.cpufp.util.PsbcBranchSecurity10Util;
//import com.psbc.cpufp.util.EncryptAndDecryptTools;
//import com.psbc.cpufp.util.HttpUtils;
//import com.psbc.cpufp.util.PsbcBranchSecurity10Service;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpHeaders;
//import org.springframework.stereotype.Service;
//import org.springframework.util.MultiValueMap;
//
//import javax.annotation.Resource;
//import javax.xml.bind.DatatypeConverter;
//import java.io.UnsupportedEncodingException;
//import java.nio.charset.StandardCharsets;
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Random;
//
///**
// * 接口日志记录
// *
// * <AUTHOR>
// */
//@Service
//public class AccInfoInquiryServiceImpl implements AccInfoInquireInterface {
//
//    private static final Logger log = LoggerFactory.getLogger(AccInfoInquiryServiceImpl.class);
//
//
//    @Resource
//    private BodyModel bodyModel;
//
//    // 安全等级10报文工具类
//    @Resource
//    private PsbcBranchSecurity10Service psbcBranchSecurity10Service;
//
//    // 安全等级10报文工具类
//    @Resource
//    private PsbcBranchSecurity10Util psbcBranchSecurity10Util;
//
//    // http请求公共类
//    @Resource
//    private HttpUtils httpUtils;
//
//    @Resource
//    private JytInterLogServiceImpl jytInterLogService;
//
//
//    @Resource
//    private EncryptAndDecryptTools encryptAndDecryptTools;
//
//
//
//    //============================常量设置
//
//    // 确认响应类型, json报文
//    Class<JSONObject> respClass = JSONObject.class;
//
//    // 确认连接超时时间, 单位ms
//    int connectTime = 5000;
//
//    // 确认响应超时时间, 单位ms
//    int readTime = 3000;
//
//    /**
//     * 请求分行前置三要素信息接口
//     */
//    @Value("${jyt.bankUrl.600368Url}")
//    private String accInfoInquiryUrl;
//
//    /**
//     * 银行证书公钥
//     */
//    @Value("${jyt.bankCert.bankCertPublicKey}")
//    private String branchCertPubKey;
//
//    /**
//     * 银行证书序列号
//     */
//    @Value("${jyt.bankCert.bankCertSn}")
//    private String bankCertSn;
//
//    /**
//     * 请求方jyt 系统号
//     */
//    @Value("${jyt.reqSysCode}")
//    private String reqSysCode;
//
//    /**
//     * 请求方jyt 证书序列号
//     */
//    @Value("${jyt.userCert.userCertSn}")
//    private String userCertSn;
//
//    /**
//     * 请求方jyt 证书公钥
//     */
//    @Value("${jyt.userCert.userCertPublicKey}")
//    private String userCertPublicKey;
//
//    /**
//     * 请求方jyt 证书私钥
//     */
//    @Value("${jyt.userCert.userCertPrivateKey}")
//    private String userCertPrivateKey;
//
//    /**
//     * ga 证书私钥
//     */
//    @Value("${jyt.gongAn.gaCertPublicKey}")
//    private String gaCertPublicKey;
//
//    /**
//     * 获取系统跟踪号
//     *
//     * @param bankType      行外系统-out，行内系统-in
//     * @param reqSysCodeStr 系统号
//     * @return 返回系统跟踪号，全局使用
//     */
//    public String getLsh(String bankType, String reqSysCodeStr) {
//        // 行外系统：如果接入系统代码是12位，则系统跟踪号（32 位）= 时间戳（14 位）  +   接入系统代码（12 位）  +   6 位唯一序列号。
//        // 如果接入系统代码是11位，则系统跟踪号（32 位）= 时间戳（14 位）  +   接入系统代码（前7 位）  +   5位实例序号   +   6位唯一序列号。
//        if (bankType.isEmpty()) { // 为空，默认为行外系统
//            bankType = "out";
//        }
//        if (reqSysCodeStr.isEmpty()) {
//            return "系统号参数为空";
//        }
//        int reqSysCodeLen = reqSysCodeStr.length();
//        String reqSysCode7 = reqSysCodeStr.substring(7);
//        // 6位随机数
//        int kk = new Random().nextInt(999999);
//        String timeNo = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
//        // 默认流水，当为空时候暂时写死，后续需要修改
//        String liushui = "20250304100620135000101871921749";
//        if ("out".equals(bankType)) {
//            if (reqSysCodeLen == 11) {
//                liushui = timeNo + reqSysCodeStr + String.format("%06d", kk);
//            } else if (reqSysCodeLen == 12) {
//                liushui = timeNo + reqSysCode7 + "J0001" + String.format("%06d", kk);
//            }
//        }
//        log.info("拼接成的系统跟踪号:", "[" + liushui + "]");
//        return liushui;
//    }
//
//    /**
//     * 加密报文并组装
//     * 请求分行前置，SM4  +  SM2
//     * 获得返回的报文信息
//     *
//     * @param reqBody 请求明文
//     * @param headers 请求头
//     */
//    public String doZonghangEnc(JSONObject reqBody, MultiValueMap<String, String> headers) throws Exception {
//        log.info("准备请求分行前置接口-卡开户信息（对应总行接口601087）-加密报文，组装并发送请求给分行前置，获取返回报文...");
//        log.info("读取gongan请求解密后的报文...");
//        //String reqData = reqBody.get("dataAfterDec").toString();
//        String reqData = reqBody.toJSONString();
//        log.info("开始解析并组装请求参数：{} ", reqData);
//        if (reqData.isEmpty()) {
//            throw new RuntimeException("未获取到解密后的报文!请检查！");
//        }
//        // jyt: 303100300001 ，12位系统号；厦门分行前置：35310030000 ，11位系统号
//        String sysTrackNo = getLsh("out", reqSysCode);
//        // 组装业务请求数据明文-查询三要素
//        JSONObject requestBodyJson = doPackageReqBodyToQz2(reqData, sysTrackNo);
//
//        // 业务请求数据明文 ，返回加密后的请求报文(整体)
//        HttpHeaders requestHeaders = new HttpHeaders(headers);
//        JSONObject qianzhiReq = psbcBranchSecurity10Service.psbcSecurity10Request(requestBodyJson, requestHeaders);
//        // Http请求分行前置接口
//        String url = accInfoInquiryUrl;
//
//        // 请求前置接口，直接获取返回信息
//        String response = HttpUtils.sendToQz(url, qianzhiReq.toString(), requestHeaders);
//        // 对返回结果进行解密
//        log.info("返回结果{}", response);
//
//        return response;
//    }
//
//
//    /**
//     * 组装请求头，向分行前置
//     * 通用
//     *
//     */
//    private HttpHeaders prepareHeaderZongHang(MultiValueMap<String, String> headers, String signStr, String sysTrackNo) {
//        // 报文体字节长度有变动，content-length如果保持之前的值会导致请求异常，此处移除，请求组装的时候框架会重新计算
//        headers.remove("content-length");
//        HttpHeaders httpHeaders = new HttpHeaders();
//        httpHeaders.addAll(headers);
//        httpHeaders.add("Content-Type", "application/json");
//        httpHeaders.add("charset", "UTF-8");
//        // sysTrackNo 业务跟踪号
//        // sysTrackNo = "20250304100620135000101871921749";
//        httpHeaders.add("sysTrackNo", sysTrackNo);
//        String txTime = new SimpleDateFormat("yyyyMMddHHmmssSSs").format(new Date());
//        // txTime 发送时间 格式:yyyyMMddHHmmssSSs
//        httpHeaders.add("txTime", txTime);
//        // reqSysCode 请求系统号
//        httpHeaders.add("regSysCode", reqSysCode); // 警银通系统号
//        // securityLevel 安全等级
//        httpHeaders.add("securityLevel", "10");
//        // sign 签名值
//        signStr = "6iPXojZ2gJ4h0VjJZ7QQlnDodEoPcUwQQo6qAwIQozIT4T9rEACa1VtvobDTli/RuO3iDTJsKnH30Z9OqQZKig==";
//        httpHeaders.add("sign", signStr);
//        // userCertSN 请求方(委托方)证书序列号
//        httpHeaders.add("userCertSN", userCertSn);
//        return httpHeaders;
//    }
//
//
//    /**
//     * 组装发往分行前置的明文数据
//     * 三要素：查询客户名下账户
//     */
//    private JSONObject doPackageReqBodyToQz2(String reqData, String sysTrackNo) {
//        ObjectMapper objectMapper = new ObjectMapper();
//        JsonNode rootNode = null;
//        JSONObject resp = null;
//        try {
//            rootNode = objectMapper.readTree(reqData);
//            JsonNode bodyNode = rootNode.get("Body");
//            JsonNode contentNode = bodyNode.get("Content");
//            if (contentNode.isEmpty()) {
//                throw new RuntimeException("查找不到解密后的content信息！");
//            }
//            String startTime = contentNode.get("StartTime").asText();
//            String endTime = contentNode.get("EndTime").asText();
//            // 1-个人，2-公司
//            String accountType = contentNode.get("AccountType").asText();
//            // 证件类型
//            String idType = contentNode.get("IdType").asText();
//            // 身份证号码
//            String accountCredentialNumber = contentNode.get("AccountCredentialNumber").asText();
//            // 文书号
//            String account = contentNode.get("Account").asText();
//            log.info("请求参数：startTime:{}, endTime:{}, accountType:{}, idType:{}, accountCredentialNumber:{}, account:{}", startTime, endTime, accountType, idType, accountCredentialNumber, account);
//            Map<String, Object> body = new HashMap<>();
//            // 流水号
//            body.put("reqSysSriNo", sysTrackNo);
//            body.put("startSysOrCmptNo", reqSysCode);
//            // 会计日期 yyyyMMdd
//            body.put("accountingDate", startTime);
//            // 操作柜员号
//            body.put("oprTellerNo", "");
//            // 业务发送系统或组件编码
//            body.put("busiSendSysOrCmptNo", "********");
//            // 客户名称
//            body.put("custNm", "");
//            // 个人证件类型代码
//            body.put("perCertTpCd", idType);
//            // 个人证件号码
//            body.put("personalCertNo", accountCredentialNumber);
//            // 发起查询机构号
//            body.put("qryInstNo", "************");
//
//            Map<String, Object> reqInfo = new HashMap<>();
//            reqInfo.put("txBody", body);
//
//            resp = new JSONObject(reqInfo);
//            return resp;
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
//
//    }
//
//    /**
//     * 获取分行前置结果后进行验签和解密
//     *
//     * @param  responseEntityZh 请求分行前置返回的加密json字符串
//     */
//    public JSONObject doZonhangDec(String responseEntityZh) throws Exception {
//        log.info("开始处理返回的密文数据：{}", responseEntityZh);
//        JSONObject obj = JSON.parseObject(responseEntityZh);
//        PsbcBranchSecurity10Service.PsbcBranchResponseComm psbcBranchResponseComm = new PsbcBranchSecurity10Service.PsbcBranchResponseComm();
//        String qianzhiResp = psbcBranchSecurity10Service.psbcSecurity10Response(obj, psbcBranchResponseComm);
//        //        //1.验签
//        //        String publicKey = ""; //服务方证书公钥
//        //        String data = ""; //待验签数据
//        //        String signedData = ""; //签名后生成的sign值
//        //        String sysTrackNo = ""; //业务跟踪号
//        //        boolean flag = VerifyTool.sm2Verify(publicKey, data, signedData, sysTrackNo);
//        //        if (!flag) {
//        //            String resp = "验签失败：" + flag;
//        //            return JSON.parseObject(resp);
//        //        }
//        //        //2.使用请求生成的sm4密钥解密
//        //        String sm4keyHexString = ""; //生成的sm4Key随机密钥
//        //        String encryptedData = ""; //加密后的报文
//        //        String respStr = DecryptTool.sm4Decrypt(sm4keyHexString, encryptedData, sysTrackNo);
//        JSONObject resp = JSON.parseObject(qianzhiResp);
//        return resp;
//    }
//
//    /**
//     * 组装请求头，向公安
//     */
//    private HttpHeaders prepareHeaderGongAn(MultiValueMap<String, String> headers) {
//        // 报文体字节长度有变动，content-length如果保持之前的值会导致请求异常，此处移除，请求组装的时候框架会重新计算
//        headers.remove("content-length");
//        HttpHeaders httpHeaders = new HttpHeaders();
//        httpHeaders.addAll(headers);
//        return httpHeaders;
//    }
//
//
//
//    /**
//     * 加密密文发往公安
//     * * 加密流程
//     * * 1、对原文A进行UTF8编码，得到原文B；
//     * * 2、发起机构每次随机生成新SM4秘钥明文F，将报文加密后转成base64发送。
//     * * 3、使用【公钥证书】对自【秘钥明文F】进行SM2加密得到秘钥【秘钥密文C，格式为16进制的字符串（无需转大写）】。
//     * * 4、将【报文密文D】保存在Content字段。将【秘钥密文C】保存在SignatureValue字段。
//     *
//     * @param rspBody 响应给公安的请求报文
//     * @return 加密后结果
//     */
//    public JSONObject doEnc(JSONObject rspBody) {
//        JSONObject body = rspBody.getJSONObject("Body");
//        String a = body.toString();
//        BodyModel bodyModel;
//        try {
//            //引用公安提供的加密方法
//            bodyModel = encryptAndDecryptTools.encryptMyDataToBankByPublicKey(a);
//        } catch (UnsupportedEncodingException e) {
//            throw new RuntimeException(e);
//        }
//        // 转成json
//        JSONObject reqContent = JSONObject.parseObject(bodyModel.toString());
//        return reqContent;
//    }
//
//    /**
//     * 加密密文发往公安
//     * * 加密流程
//     * * 1、对原文A进行UTF8编码，得到原文B；
//     * * 2、发起机构每次随机生成新SM4秘钥明文F，将报文加密后转成base64发送。
//     * * 3、使用【公钥证书】对自【秘钥明文F】进行SM2加密得到秘钥【秘钥密文C，格式为16进制的字符串（无需转大写）】。
//     * * 4、将【报文密文D】保存在Content字段。将【秘钥密文C】保存在SignatureValue字段。
//     *
//     * @param rspBody 响应给公安的请求报文
//     * @return 加密后结果
//     */
//    public JSONObject doEncBak(JSONObject rspBody) {
//
//        // 1. 显式UTF-8编码处理（A转化成原文B）
//        byte[] utf8Bytes = rspBody.getBytes("UTF-8");
//        String plainTextB = new String(utf8Bytes, StandardCharsets.UTF_8);
//
//        // 2. 生成随机SM4密钥（秘钥明文F）
//        String sm4KeyF = EncryptionUtils.randSm4SecretKey();
//        byte[] sm4KeyBytes = DatatypeConverter.parseHexBinary(sm4KeyF);
//        // 打印密钥前20个字符
//        log.info("随机SM4密钥前20个字符:", sm4KeyF.substring(20));
//        //  使用F对报文进行SM4加密，得到报文密文G，转为base64格式的报文密文D
//        String cipherTextD = EncryptionUtils.sm4Encrypt(sm4KeyBytes, plainTextB);
//
//        // 3. 使用【公钥】E 对【秘钥明文F】进行SM2加密得到密钥16进制【秘钥密文C】, 银行提供的证书cer
//        String cerPublic = userCertPublicKey;
//        String cipherKeyC = EncryptionUtils.sm2EncryptByPublicKey(cerPublic, sm4KeyBytes);
//        cipherKeyC = cipherKeyC.toUpperCase();   // 强制转为大写十六进制
//
//        // 4、将【报文密文D】保存在Content字段。将【秘钥密文C】保存在SignatureValue字段。
//        BodyModel model = new BodyModel();
//        model.setContent(cipherTextD);
//        model.setSignatureValue(cipherKeyC);
//
//        // 转成json
//        JSONObject reqContent = JSONObject.parseObject(model.toString());
//        return reqContent;
//    }
//
//    /**
//     * 传入加密报文，委托方加密随机密钥，加签信息
//     * 返回报文组装结果
//     * head:
//     * txTime 发送时间 格式:yyyyMMddHHmmssSSs
//     * regSysCode 请求系统号
//     * securityLevel 安全等级
//     * sign 签名值
//     * userCertSN 请求方(委托方)证书序列号
//     * txComm.bankCertSN 服务方(银行端)证书序列号
//     * txComm.encData 加密后的报文
//     * txComm.encKey sm4key随机秘钥加密后的值
//     */
////    private String doMsgToQianzh(String msgB, String sm4KeyEnc, String signStr) throws JsonProcessingException {
////
////        // 组装业务请求-向分行前置
////        // body
////        // txComm.bankCertSN 服务方(银行端)证书序列号
////        // txComm.encData 加密后的报文
////        // txComm.encKey sm4key随机秘钥加密后的值
////        JSONObject resp = null;
////        // 最内层 Map
////        Map<String, Object> encInfo = new HashMap<>();
////        encInfo.put("bankCertSN", "");
////        encInfo.put("encData", "");
////        encInfo.put("encKey", "");
////
////        Map<String, Object> txcomm = new HashMap<>();
////        txcomm.put("txComm", encInfo);
////
////        Map<String, Object> head = new HashMap<>();
////        // 流水号
////        head.put("reqSysSriNo", "");
////        head.put("startSysOrCmptNo", reqSysCode);
////        head.put("busiSendInstNo", "************");
////
////        Map<String, Object> body = new HashMap<>();
////        body.put("mediumNo", "accountCredentialNumber");
////
////        Map<String, Object> txentity = new HashMap<>();
////        txentity.put("txEntity", body);
////
////        Map<String, Object> txbody = new HashMap<>();
////        txbody.put("txBody", txentity);
////
////
////        Map<String, Object> reqInfo = new HashMap<>();
////        reqInfo.put("txHead", head);
////        reqInfo.put("txBody", txbody);
////
////        // 使用 Jackson 将 Map 转换为 JSON 字符串
////        ObjectMapper objectMapper = new ObjectMapper();
////        String reqContent = objectMapper.writeValueAsString(reqInfo);
////        return reqContent;
////    }
////
//
//
//
//}
