package com.psbc.socket;

import com.psbc.util.ConfigUtil;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import org.apache.log4j.Logger;

public class PayServerSocket extends Thread {
  private static final Logger logger = Logger.getLogger(PayServerSocket.class);
  
  private ServerSocket serverSocket;
  
  public PayServerSocket() {
    if (this.serverSocket == null) {
      String port = ConfigUtil.getvalue("WG_PORT");
      if (port == null) {
    	 logger.error("读取socket服务端监听端口失败");
      } else {
    	 logger.info("socket服务端监听端囗port:"+port);
      } 
      try {
        this.serverSocket = new ServerSocket(Integer.parseInt(port));
        logger.info("socket服务端启动成功");
      } catch (NumberFormatException e) {
    	logger.info("socket服务端启动失败");
        e.printStackTrace();
      } catch (IOException e) {
    	logger.info("socket服务端启动失败");
        e.printStackTrace();
      } 
    } 
  }
  
  public void stopserver() {
    try {
      if (!this.serverSocket.isClosed()) {
        this.serverSocket.close();
        logger.info("socket服务端停止");
      } 
    } catch (IOException e) {
    	logger.error("socket服务端停止异常",e);
    } 
  }
  
  public void run() {
    Socket socket = null;
    while (!this.serverSocket.isClosed()) {
      try {
        socket = this.serverSocket.accept();
        if (socket != null) {
        	logger.error("新建socket处理线程开始>>>");
          (new ServerThread(socket)).start();
        } 
      } catch (IOException e) {
    	logger.error("serversocket监听异常");
        e.printStackTrace();
      } catch (Exception e) {
    	logger.error("serversocket监听异常");
        e.printStackTrace();
      } 
    } 
  }
}
