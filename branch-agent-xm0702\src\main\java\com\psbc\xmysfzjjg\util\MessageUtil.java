package com.psbc.xmysfzjjg.util;

import com.alibaba.fastjson.JSON;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 消息处理工具类
 */
@Component
public class MessageUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageUtil.class);
    
    /**
     * 判断字符串是否为JSON格式
     */
    public static boolean isJson(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        try {
            JSON.parseObject(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 判断字符串是否为XML格式
     */
    public static boolean isXml(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        try {
            DocumentHelper.parseText(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 格式化JSON字符串
     */
    public static String formatJson(String jsonStr) {
        try {
            Object obj = JSON.parse(jsonStr);
            return JSON.toJSONString(obj, true);
        } catch (Exception e) {
            logger.warn("格式化JSON失败", e);
            return jsonStr;
        }
    }
    
    /**
     * 创建标准错误响应（JSON格式）
     */
    public static String createJsonErrorResponse(String errorCode, String errorMessage) {
        return String.format("{\"status\":\"%s\",\"message\":\"%s\"}", errorCode, errorMessage);
    }
    
    /**
     * 创建标准错误响应（XML格式）
     */
    public static String createXmlErrorResponse(String errorCode, String errorMessage) {
        return String.format("<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><statecode>%s</statecode><msg>%s</msg></head></content>", 
                errorCode, errorMessage);
    }
    
    /**
     * 创建标准成功响应（JSON格式）
     */
    public static String createJsonSuccessResponse(Object data) {
        return JSON.toJSONString(data);
    }
}
