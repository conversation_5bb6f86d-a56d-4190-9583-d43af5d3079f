package com.psbc.xmysfzjjg.service;

import cn.hutool.http.HttpRequest;
import com.psbc.xmysfzjjg.util.ConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;

/**
 * 综合办公服务
 * 整合原有ServerThread中的综合办公请求逻辑
 */
@Service
public class ZhbgService {
    
    private static final Logger logger = LoggerFactory.getLogger(ZhbgService.class);
    
    @Autowired
    private ConfigService configService;
    
    /**
     * 发送综合办公请求
     */
    public String sendZhbgRequest(String requestMessage, String serviceNo) throws Exception {
        String baseUrl = configService.getZhbgValue("zhbgPostUrl");
        String zhbgUrl;
        
        if ("30001".equals(serviceNo)) {
            zhbgUrl = baseUrl + configService.getZhbgValue("url30001");
            logger.info("开始请求30001接口，URL: {}", zhbgUrl);
            logger.info("传入JSON参数: {}", requestMessage);
            
            return sendJsonRequest(zhbgUrl, requestMessage);
            
        } else if ("20006".equals(serviceNo)) {
            zhbgUrl = baseUrl + configService.getZhbgValue("url20006");
            logger.info("开始请求20006接口，URL: {}", zhbgUrl);
            logger.info("传入XML参数: {}", requestMessage);
            
            return sendXmlRequest(zhbgUrl, requestMessage);
        }
        
        throw new IllegalArgumentException("不支持的服务号: " + serviceNo);
    }
    
    /**
     * 发送JSON请求
     */
    private String sendJsonRequest(String url, String jsonData) throws Exception {
        try {
            // 使用Hutool发送HTTP请求
            String response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(jsonData)
                    .execute()
                    .body();
            
            logger.info("JSON请求响应: {}", response);
            return response;
            
        } catch (Exception e) {
            logger.error("发送JSON请求异常", e);
            throw e;
        }
    }
    
    /**
     * 发送XML请求
     */
    private String sendXmlRequest(String urlStr, String xmlData) throws Exception {
        OutputStreamWriter out = null;
        BufferedReader in = null;

        try {
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            // 如果是HTTPS连接，设置信任所有证书
            if (conn instanceof HttpsURLConnection) {
                setupTrustAllCerts((HttpsURLConnection) conn);
            }

            // 创建连接
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-type", "application/xml");
            conn.connect();
            
            // 发送数据
            out = new OutputStreamWriter(conn.getOutputStream(), "utf-8");
            out.write(xmlData);
            out.flush();
            
            // 读取响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                response.append(line);
            }
            
            String responseStr = response.toString();
            logger.info("XML请求响应: {}", responseStr);
            return responseStr;
            
        } catch (Exception e) {
            logger.error("发送XML请求异常", e);
            throw e;
        } finally {
            if (out != null) {
                try { out.close(); } catch (Exception e) { /* ignore */ }
            }
            if (in != null) {
                try { in.close(); } catch (Exception e) { /* ignore */ }
            }
        }
    }
    
    /**
     * 设置信任所有SSL证书
     */
    private void setupTrustAllCerts(HttpsURLConnection conn) throws Exception {
        X509TrustManager tm = new X509TrustManager() {
            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }
            
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
                // 信任所有证书
            }
            
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
                // 信任所有证书
            }
        };
        
        javax.net.ssl.SSLContext sslContext = javax.net.ssl.SSLContext.getInstance("TLS");
        sslContext.init(null, new TrustManager[]{tm}, null);
        conn.setSSLSocketFactory(sslContext.getSocketFactory());
        conn.setHostnameVerifier((hostname, session) -> true);
    }
}
