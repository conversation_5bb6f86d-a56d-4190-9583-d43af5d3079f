//package com.psbc.cpufp.controller;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.JsonNode;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.psbc.cpufp.faced.RestTemplateFacade;
//import com.psbc.cpufp.util.EncryptionUtils;
//import com.psbc.cpufp.util.BodyModel;
//import com.psbc.cpufp.util.EncryptAndDecryptTools;
//import com.psbc.cpufp.util.HttpUtils;
//import com.psbc.cpufp.util.PsbcBranchSecurity10Service;
//import com.psbc.cpufp.util.Sm2Util;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpHeaders;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Random;
//
///**
// * 总行接口测试
// */
//@RestController
//@Slf4j
//public class ZhReqController {
//
//    private static final Logger log = LoggerFactory.getLogger(ZhReqController.class);
//
//    @Autowired
//    private RestTemplateFacade restTemplateFacade;
//
//    @Resource
//    private EncryptionUtils encryptionUtils;
//
//    @Resource
//    private BodyModel bodyModel;
//
//    @Resource
//    private EncryptAndDecryptTools encryptAndDecryptTools;
//
//    // 安全等级10报文工具类
//    @Resource
//    private PsbcBranchSecurity10Service psbcBranchSecurity10Service;
//
//    //sm2加解密工具类
//    @Resource
//    private Sm2Util sm2Util;
//
//
//    /**
//         * 请求分行前置卡开户信息接口
//     */
//    @Value("${jyt.bankUrl.601087Url}")
//    private String kakaihuUrl;
//
//    /**
//     * 请求分行前置三要素信息接口
//     */
//    @Value("${jyt.bankUrl.600368Url}")
//    private String accInfoInquiryUrl;
//
//    /**
//     * 银行证书公钥
//     */
//    @Value("${jyt.bankCert.bankCertPublicKey}")
//    private String branchCertPubKey;
//
//    /**
//     * 银行证书序列号
//     */
//    @Value("${jyt.bankCert.bankCertSn}")
//    private String bankCertSn;
//
//    /**
//     * 请求方jyt 系统号
//     */
//    @Value("${jyt.reqSysCode}")
//    private String reqSysCode;
//
//    /**
//     * 请求方jyt 证书序列号
//     */
//    @Value("${jyt.userCert.userCertSn}")
//    private String userCertSn;
//
//    /**
//     * 请求方jyt 证书公钥
//     */
//    @Value("${jyt.userCert.userCertPublicKey}")
//    private String userCertPublicKey;
//
//    /**
//     * 请求方jyt 证书私钥
//     */
//    @Value("${jyt.userCert.userCertPrivateKey}")
//    private String userCertPrivateKey;
//
//
//
//    /**
//     * 查询三要素接口获得返回值
//     *
//     * @param reqBody 请求报文
//     * @param headers 请求header
//     * @return 响应结果
//     */
//    @PostMapping("/sanyaosu")
//    public JSONObject sanyaosu(@RequestBody JSONObject reqBody, @RequestHeader MultiValueMap<String, String> headers) {
//        log.info("进入/sanyaosu 接口，请求参数reqBody:{}, headers:{}", reqBody.toJSONString(), headers.toString());
//
//        try {
//            // 公安请求报文（解密）
//            //JSONObject remoteRequestBody = doDec(reqBody);
//            //总行请求报文
//            JSONObject remoteRequestBody = reqBody;
//            // 请求分行前置接口-三要素信息（对应接口J600368）-加密报文，组装并发送请求给分行前置，获取返回报文
//            //JSONObject sm2ReqJson = null;
//            String responseEntityQianzhi = doZonghangEnc600368(remoteRequestBody, headers);
//            log.info("1-请求分行前置接口-三要素信息（对应接口J600368）密文:{}", responseEntityQianzhi);
//            // 解析总行响应（解密）明文
//            JSONObject zhRespInfo = doZonhangDec600368(responseEntityQianzhi);
//            log.info("2-解析总行响应（解密）明文:{}", zhRespInfo.toJSONString());
//            return zhRespInfo;
//        } catch (Exception e) {
//            log.error("三要素信息（对应接口J600368）接口请求 EncryptMessage failed | Error: {}", e.getMessage());
//            throw new RuntimeException("Encryption process failed", e);
//        } finally {
//            log.info("三要素信息（对应接口J600368）接口请求结束！ ");
//        }
//
//    }
//
//
//
//    /**
//     * 查询卡开户接口获得返回值
//     *
//     * @param reqBody 请求报文
//     * @param headers 请求header
//     * @return 响应结果
//     */
//    @PostMapping("/kakaihu")
//    public JSONObject cardInstall(@RequestBody JSONObject reqBody, @RequestHeader MultiValueMap<String, String> headers) {
//        log.info("进入/kakaihu 接口，请求参数reqBody:{}, headers:{}", reqBody.toJSONString(), headers.toString());
//        try {
//            // 公安请求报文（解密）
//            //JSONObject remoteRequestBody = doDec(reqBody);
//            // 请求分行前置接口-卡开户信息（对应接口J601087）-加密报文，组装并发送请求给分行前置，获取返回报文
//            JSONObject remoteRequestBody = reqBody;
//            String responseEntityQianzhi = doZonghangEnc(remoteRequestBody, headers);
//            log.info("1-请求分行前置接口-卡开户信息（对应接口J601087）密文:{}", responseEntityQianzhi);
//            // 解析总行响应（解密）明文
//            JSONObject zhRespInfo = doZonhangDec(responseEntityQianzhi);
//            log.info("2-解析总行响应（解密）明文:{}", zhRespInfo.toJSONString());
//            return zhRespInfo;
//        } catch (Exception e) {
//            log.error("EncryptMessage failed | Error: {}", e.getMessage());
//            throw new RuntimeException("Encryption process failed", e);
//        } finally {
//            log.info("请求卡开户接口结束！");
//        }
//
//    }
//
//
//
//
//    //===========================公用相关方法===============================
//    /**
//     * 获取系统跟踪号
//     *
//     * @param bankType      行外系统-out，行内系统-in
//     * @param reqSysCodeStr 系统号
//     * @return 返回系统跟踪号，全局使用
//     */
//    public static String getLsh(String bankType, String reqSysCodeStr) {
//        // 行外系统：如果接入系统代码是12位，则系统跟踪号（32 位）= 时间戳（14 位）  +   接入系统代码（12 位）  +   6 位唯一序列号。
//        // 如果接入系统代码是11位，则系统跟踪号（32 位）= 时间戳（14 位）  +   接入系统代码（前7 位）  +   5位实例序号   +   6位唯一序列号。
//        if (bankType.isEmpty()) { // 为空，默认为行外系统
//            bankType = "out";
//        }
//        if (reqSysCodeStr.isEmpty()) {
//            return "系统号参数为空";
//        }
//        int reqSysCodeLen = reqSysCodeStr.length();
//        String reqSysCode7 = reqSysCodeStr.substring(7);
//        // 6位随机数
//        int kk = new Random().nextInt(999999);
//        String timeNo = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
//        // 默认流水，当为空时候暂时写死，后续需要修改
//        String liushui = "20250304100620135000101871921749";
//        if ("out".equals(bankType)) {
//            if (reqSysCodeLen == 11) {
//                liushui = timeNo + reqSysCodeStr + String.format("%06d", kk);
//            } else if (reqSysCodeLen == 12) {
//                liushui = timeNo + reqSysCode7 + "J0001" + String.format("%06d", kk);
//            }
//        }
//        log.info("拼接成的系统跟踪号:", "[" + liushui + "]");
//        return liushui;
//    }
//
//
//    //===========================三要素接口引用相关方法===============================
//
//    /**
//     * 三要素接口
//     * 加密报文并组装
//     * 请求分行前置，SM4  +  SM2
//     * 获得返回的报文信息-密文
//     *
//     * @param reqBody 请求明文
//     * @param headers 请求头
//     */
//    private String doZonghangEnc600368(JSONObject reqBody, MultiValueMap<String, String> headers) throws Exception {
//        log.info("读取请求总行的明文报文...");
//        // 业务请求数据明文 ，返回加密后的请求报文(整体)
//        HttpHeaders requestHeaders = new HttpHeaders(headers);
//        // 报文体字节长度有变动，content-length如果保持之前的值会导致请求异常，此处移除，请求组装的时候框架会重新计算
//        requestHeaders.remove("content-length");
//        // jyt: 303100300001 ，12位系统号；厦门分行前置：35310030000 ，11位系统号
//        String reqSysSriNo = getLsh("out", reqSysCode);
//        // 组装业务请求数据明文-查询三要素接口。传入 请求明文，流水号
//        JSONObject requestBodyJson = doPackageReqBodyToQz600368(reqBody.toJSONString(), reqSysSriNo);
//        // 组装加密报文
//        JSONObject qianzhiReq = psbcBranchSecurity10Service.psbcSecurity10Request(requestBodyJson, requestHeaders);
//        // Http请求分行前置接口
//        String url = accInfoInquiryUrl;
//        // 请求前置接口，直接获取返回信息-密文
//        String response = HttpUtils.sendToQz(url, qianzhiReq.toString(), requestHeaders);
//        log.info("返回结果{}", response);
//        return response;
//    }
//
//    /**
//     * 三要素接口
//     * 组装发往分行前置的明文数据
//     */
//    private JSONObject doPackageReqBodyToQz600368(String reqData, String reqSysSriNo) {
//        Map<String, Object> body = new HashMap<>();
//        body.put("reqSysSriNo", reqSysSriNo); // 流水号
//        body.put("startSysOrCmptNo", reqSysCode); //发起系统或组件编码
//        String startTime = "********";
//        body.put("accountingDate", startTime); // 会计日期 yyyyMMdd
//
//        // 操作柜员号
//        body.put("oprTellerNo", "***********");
//        // 业务发送系统或组件编码
//        body.put("busiSendSysOrCmptNo", "********");
//        // 客户名称 -sm2加密
//        String custNm = sm2Util.sm2String("邱某萍");
//        body.put("custNm", custNm);
//        // 个人证件类型代码 -sm2加密
//        String idType = sm2Util.sm2String("001");
//        body.put("perCertTpCd", idType);
//        // 个人证件号码 -sm2加密
//        String personalCertNo = sm2Util.sm2String("350204197012312025");
//        body.put("personalCertNo", personalCertNo);
//        // 发起查询机构号,非必填
//        body.put("qryInstNo", "************");
//
//        Map<String, Object> reqInfo = new HashMap<>();
//        reqInfo.put("txBody", body);
//        JSONObject resp = null;
//        resp = new JSONObject(reqInfo);
//        return resp;
//
//    }
//
//    /**
//     * 获取分行前置结果后进行验签和解密-三要素
//     */
//    private JSONObject doZonhangDec600368(String responseEntityZh) throws Exception {
//        log.info("开始处理返回的密文数据：{}", responseEntityZh);
//        JSONObject obj = JSON.parseObject(responseEntityZh);
//        PsbcBranchSecurity10Service.PsbcBranchResponseComm psbcBranchResponseComm = new PsbcBranchSecurity10Service.PsbcBranchResponseComm();
//        String qianzhiResp = psbcBranchSecurity10Service.psbcSecurity10Response(obj, psbcBranchResponseComm);
//        JSONObject resp = JSON.parseObject(qianzhiResp);
//        return resp;
//    }
//
//    //===========================卡开户接口引用相关方法===============================
//
//    /**
//     * 卡开户接口
//     * 组装发往分行前置的明文数据
//     * 查询卡开户信息
//     */
//    private JSONObject doPackageReqBodyToQz(String reqData, String reqSysSriNo) {
//        ObjectMapper objectMapper = new ObjectMapper();
//        JsonNode rootNode = null;
//        JSONObject resp = null;
//        try {
//            //解析总行明文
//            rootNode = objectMapper.readTree(reqData);
//            JsonNode txBody = rootNode.get("txBody");
//            JsonNode txEntity = txBody.get("txEntity");
//            String mediumNo = txEntity.get("mediumNo").asText();
//            Map<String, Object> body = new HashMap<>();
//            body.put("mediumNo", mediumNo);
//
//            Map<String, Object> txentity = new HashMap<>();
//            txentity.put("txEntity", body);
//
//            Map<String, Object> head = new HashMap<>();
//            JsonNode txHeader = rootNode.get("txHeader");
//            reqSysSriNo = txHeader.get("reqSysSriNo").asText();
//            // 流水号
//            head.put("reqSysSriNo", reqSysSriNo);
//            String startSysOrCmptNo = txHeader.get("startSysOrCmptNo").asText();
//            head.put("startSysOrCmptNo", startSysOrCmptNo);
//            String busiSendInstNo = txHeader.get("busiSendInstNo").asText();
//            head.put("busiSendInstNo", busiSendInstNo);
//            head.put("Content-Type", "application/json");
//
//            Map<String, Object> txbody = new HashMap<>();
//            txbody.put("txBody", txentity);
//
//
//            Map<String, Object> reqInfo = new HashMap<>();
//            reqInfo.put("txHead", head);
//            reqInfo.put("txBody", txbody);
//
//            // 将 Map 转换为 JSONObject对象
//            resp = new JSONObject(reqInfo);
//            return resp;
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
//
//    }
//
//    /**
//     * 加密报文并组装
//     * 请求分行前置，SM4  +  SM2
//     * 获得返回的报文信息
//     *
//     * @param reqBody 请求明文
//     * @param headers 请求头
//     */
//    private String doZonghangEnc(JSONObject reqBody, MultiValueMap<String, String> headers) throws Exception {
//        log.info("读取请求总行的明文报文...");
//
//        // 业务请求数据明文 ，返回加密后的请求报文(整体)
//        HttpHeaders requestHeaders = new HttpHeaders();
//        // 报文体字节长度有变动，content-length如果保持之前的值会导致请求异常，此处移除，请求组装的时候框架会重新计算
//        requestHeaders.remove("content-length");
//        // sysTrackNo 业务跟踪号
//        // sysTrackNo = "20250304100620135000101871921749";
//        // jyt: 303100300001 ，12位系统号；厦门分行前置：35310030000 ，11位系统号
//        String reqSysSriNo = getLsh("out", reqSysCode);
//        // 组装业务请求数据明文-查询卡开户
//        JSONObject requestBodyJson = doPackageReqBodyToQz(reqBody.toJSONString(), reqSysSriNo);
//        JSONObject qianzhiReq = psbcBranchSecurity10Service.psbcSecurity10Request(requestBodyJson, requestHeaders);
//        // Http请求分行前置接口-卡开户
//        String url = kakaihuUrl;
//        // 请求前置接口，直接获取返回信息
//        String response = HttpUtils.sendToQz(url, qianzhiReq.toString(), requestHeaders);
//        // 对返回结果进行解密
//        log.info("返回结果{}", response);
//        return response;
//    }
//
//
//
//
//    /**
//     * 获取分行前置结果后进行验签和解密
//     */
//    private JSONObject doZonhangDec(String responseEntityZh) throws Exception {
//        log.info("开始处理返回的密文数据：{}", responseEntityZh);
//        JSONObject obj = JSON.parseObject(responseEntityZh);
//        PsbcBranchSecurity10Service.PsbcBranchResponseComm psbcBranchResponseComm = new PsbcBranchSecurity10Service.PsbcBranchResponseComm();
//        String qianzhiResp = psbcBranchSecurity10Service.psbcSecurity10Response(obj, psbcBranchResponseComm);
//        JSONObject resp = JSON.parseObject(qianzhiResp);
//        return resp;
//    }
//}
