//package com.psbc.cpufp.entity.enu.generatekeyenum;
//
//import com.psbc.cpufp.common.cache.service.RedisKeyInterface;
//import com.psbc.cpufp.common.cache.service.TableInfoInterface;
//import com.psbc.cpufp.constant.KeyModelConstant;
//import com.psbc.cpufp.entity.enu.tableinfoenum.FrontTxCodeInfo4RedisEnum;
//
///**
// * 单表查询：
// * tableFields建议和数据库字段保持一致-》同时mapper.xml取值时使用：#{tableFields},如：
// * select front_txname from tb_front_txcode_info where front_txcode = #{front_txcode} and flag = #{flag}
// *
// * <AUTHOR>
// */
//public enum TbFrontTxcodeInfo4RedisEnum implements RedisKeyInterface {
//    /**
//     * redisKey枚举：类型-版本号-值
//     */
//    FRONT_TX_CODE_FLAG_20230809(KeyModelConstant.KEY_TYPE_DB, "20230809", FrontTxCodeInfo4RedisEnum.FRONT_TX_CODE,
//            FrontTxCodeInfo4RedisEnum.FLAG),
//
//
//    /**
//     * redisKey枚举：类型-版本号-值
//     */
//    FRONT_TX_CODE_FLAG_20240809(KeyModelConstant.KEY_TYPE_DB, "20240809", FrontTxCodeInfo4RedisEnum.FRONT_TX_CODE);
//
//    TbFrontTxcodeInfo4RedisEnum(String type, String version, TableInfoInterface... tableFields) {
//        this.type = type;
//        this.tableFields = tableFields;
//        this.version = version;
//    }
//
//    /**
//     * 字段名
//     */
//    private TableInfoInterface[] tableFields;
//
//    /**
//     * 版本号
//     */
//    private String version;
//    /**
//     * 类型
//     */
//    private String type;
//
//    public String getVersion() {
//        return version;
//    }
//
//    public String getType() {
//        return type;
//    }
//
//    @Override
//    public TableInfoInterface[] getTableFields() {
//        return tableFields;
//    }
//}
