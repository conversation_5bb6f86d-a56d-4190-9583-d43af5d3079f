//package com.psbc.cpufp.entity.model;
//
//import com.psbc.cpufp.entity.info.FileInfo;
//import lombok.Data;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * NoticeParam
// */
//@Data
//public class NoticeParam {
//    /**
//     * 文件名称
//     */
//    private String originalFileName;
//
//    /**
//     * 结果中fastDFS文件信息
//     */
//    private List<FileInfo> fastDfsFileResultInfo;
//    /**
//     * 结果中服务器文件信息文件信息
//     */
//    private List<ServerFileInfo> serviceFileResultInfo;
//
//    /**
//     * 统一的扩展信息
//     */
//    private Map<String, Object> extendInfo;
//}
