//package com.psbc.cpufp.service.impl;
//
//import com.psbc.cpufp.common.cache.service.GetRedisKeyInterface;
//import com.psbc.cpufp.common.cache.service.RedisKeyInterface;
//import com.psbc.cpufp.entity.enu.generatekeyenum.TbFrontCodeUniteMapInfo4RedisEnum;
//import com.psbc.cpufp.entity.enu.generatekeyenum.TbFrontTxcodeInfo4RedisEnum;
//import org.springframework.stereotype.Component;
//
//import java.util.HashSet;
//import java.util.Set;
//
///**
// * 将所有redis的key收集到set中,后面可以通过表名和数据获取对应redis的key
// *
// * <AUTHOR>
// */
//@Component
//public class GetRedisKeyImpl implements GetRedisKeyInterface {
//    @Override
//    public Set<Class<? extends RedisKeyInterface>> getRedisKeyEnumClass() {
//        Set<Class<? extends RedisKeyInterface>> classesSet = new HashSet<>();
//        classesSet.add(TbFrontTxcodeInfo4RedisEnum.class);
//        classesSet.add(TbFrontCodeUniteMapInfo4RedisEnum.class);
//        return classesSet;
//    }
//}
