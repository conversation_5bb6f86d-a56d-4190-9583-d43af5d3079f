package com.psbc.xmysfzjjg.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 综合办公配置属性类
 * 替代原有的ZhbgConfigUtil
 */
@Component
@ConfigurationProperties(prefix = "xmysfzjjg.zhbg")
public class ZhbgProperties {
    
    /**
     * 综合办公基础URL
     */
    private String postUrl;
    
    /**
     * 30001接口路径 - 查询按揭贷款
     */
    private String url30001 = "api/admin/xzp/queryYxjfLsxdye";
    
    /**
     * 20006接口路径 - 查询监管账户被执行变动情况反馈信息
     */
    private String url20006 = "api/admin/xzp/queryJgzhInfo";
    
    /**
     * 请求系统号
     */
    private String reqSysNo = "350200001";
    
    /**
     * 综合办公公钥
     */
    private String zhbgPublicKey = "0438b7e7107b34d58ace4edea7a3526ba43b10cb430b1e29fe652c8acae44534f967837760983a41ae3d96635623abd70187023a5f21a67c7af6966e06e728c7f3";
    
    /**
     * 自身公钥
     */
    private String selfPublicKey;
    
    /**
     * 私钥
     */
    private String privateKey;

    // Getters and Setters
    public String getPostUrl() {
        return postUrl;
    }

    public void setPostUrl(String postUrl) {
        this.postUrl = postUrl;
    }

    public String getUrl30001() {
        return url30001;
    }

    public void setUrl30001(String url30001) {
        this.url30001 = url30001;
    }

    public String getUrl20006() {
        return url20006;
    }

    public void setUrl20006(String url20006) {
        this.url20006 = url20006;
    }

    public String getReqSysNo() {
        return reqSysNo;
    }

    public void setReqSysNo(String reqSysNo) {
        this.reqSysNo = reqSysNo;
    }

    public String getZhbgPublicKey() {
        return zhbgPublicKey;
    }

    public void setZhbgPublicKey(String zhbgPublicKey) {
        this.zhbgPublicKey = zhbgPublicKey;
    }

    public String getSelfPublicKey() {
        return selfPublicKey;
    }

    public void setSelfPublicKey(String selfPublicKey) {
        this.selfPublicKey = selfPublicKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }
}
