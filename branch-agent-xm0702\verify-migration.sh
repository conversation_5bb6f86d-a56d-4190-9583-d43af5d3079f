#!/bin/bash

echo "========================================"
echo "XMYSFZJJG 迁移验证脚本"
echo "========================================"

echo ""
echo "1. 检查应用是否运行..."
if curl -s http://localhost:6666/test/health > /dev/null 2>&1; then
    echo "✓ 应用正在运行"
else
    echo "✗ 应用未运行，请先启动应用"
    echo "  使用命令: mvn spring-boot:run -Dspring.profiles.active=local"
    exit 1
fi

echo ""
echo "2. 测试健康检查接口..."
curl -s http://localhost:6666/test/health
echo ""

echo ""
echo "3. 测试配置接口..."
curl -s http://localhost:6666/test/config
echo ""

echo ""
echo "4. 测试WebService WSDL..."
if curl -s http://localhost:6666/wservices/IWebServiceService?wsdl | grep -q "wsdl:definitions"; then
    echo "✓ WebService WSDL 可访问"
else
    echo "✗ WebService WSDL 不可访问"
fi

echo ""
echo "5. 测试Socket连接..."
echo "运行Socket测试..."
java -cp "target/classes:target/test-classes" com.psbc.xmysfzjjg.SimpleSocketTest

echo ""
echo "6. 测试综合办公接口..."
echo "测试30001接口..."
curl -X POST -H "Content-Type: application/json" -d '{"serviceno":"30001","test":"data"}' http://localhost:6666/api/admin/xzp/queryYxjfLsxdye
echo ""

echo ""
echo "========================================"
echo "验证完成！"
echo "========================================"
echo ""
echo "如果所有测试都通过，说明迁移成功！"
echo ""
echo "服务端点："
echo "- WebService: http://localhost:6666/wservices/IWebServiceService?wsdl"
echo "- REST API: http://localhost:6666/api/admin/xzp/"
echo "- Socket: localhost:8888"
echo "- 健康检查: http://localhost:6666/test/health"
echo ""
