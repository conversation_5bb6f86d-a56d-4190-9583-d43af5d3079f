package com.psbc.xmysfzjjg.netty;

import com.alibaba.fastjson.JSON;
import com.psbc.xmysfzjjg.service.MessageProcessService;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * Socket消息处理器
 * 替代原有的ServerThread
 */
@Component
public class SocketMessageHandler extends SimpleChannelInboundHandler<String> {
    
    private static final Logger logger = LoggerFactory.getLogger(SocketMessageHandler.class);
    
    @Autowired
    private MessageProcessService messageProcessService;
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        logger.info("新的客户端连接: {}", ctx.channel().remoteAddress());
        super.channelActive(ctx);
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.info("客户端断开连接: {}", ctx.channel().remoteAddress());
        super.channelInactive(ctx);
    }
    
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, String msg) throws Exception {
        logger.info("接收到消息: {}", msg);
        
        try {
            // 处理消息并获取响应
            String response = messageProcessService.processMessage(msg);
            
            // 发送响应
            if (response != null) {
                ctx.writeAndFlush(response);
                logger.info("发送响应: {}", response);
            }
            
        } catch (Exception e) {
            logger.error("处理消息时发生异常", e);
            // 发送错误响应
            String errorResponse = createErrorResponse("处理消息时发生异常: " + e.getMessage());
            ctx.writeAndFlush(errorResponse);
        }
    }
    
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            if (event.state() == IdleState.ALL_IDLE) {
                logger.info("连接空闲超时，关闭连接: {}", ctx.channel().remoteAddress());
                ctx.close();
            }
        }
        super.userEventTriggered(ctx, evt);
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("连接异常: {}", ctx.channel().remoteAddress(), cause);
        ctx.close();
    }
    
    /**
     * 创建错误响应
     */
    private String createErrorResponse(String errorMsg) {
        HashMap<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("status", "error");
        errorResponse.put("message", errorMsg);
        return JSON.toJSONString(errorResponse);
    }
}
