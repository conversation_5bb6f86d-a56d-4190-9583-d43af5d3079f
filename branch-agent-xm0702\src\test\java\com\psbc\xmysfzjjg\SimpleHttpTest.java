package com.psbc.xmysfzjjg;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 简单的HTTP测试
 */
public class SimpleHttpTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleHttpTest.class);
    
    public static void main(String[] args) {
        testHealthCheck();
        testConfigApi();
    }
    
    public static void testHealthCheck() {
        try {
            URL url = new URL("http://localhost:6666/test/health");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            
            int responseCode = conn.getResponseCode();
            logger.info("健康检查响应码: {}", responseCode);
            
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                logger.info("健康检查响应: {}", response.toString());
            }
            
        } catch (Exception e) {
            logger.error("健康检查失败", e);
        }
    }
    
    public static void testConfigApi() {
        try {
            URL url = new URL("http://localhost:6666/test/config");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            
            int responseCode = conn.getResponseCode();
            logger.info("配置API响应码: {}", responseCode);
            
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                logger.info("配置API响应: {}", response.toString());
            }
            
        } catch (Exception e) {
            logger.error("配置API测试失败", e);
        }
    }
}
