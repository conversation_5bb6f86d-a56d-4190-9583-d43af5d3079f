package com.psbc.xmysfzjjg.controller;

import com.psbc.xmysfzjjg.config.NettyProperties;
import com.psbc.xmysfzjjg.config.XmysfzjjgProperties;
import com.psbc.xmysfzjjg.config.ZhbgProperties;
import com.psbc.xmysfzjjg.util.ConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
public class TestController {
    
    @Autowired
    private ConfigService configService;
    
    @Autowired
    private XmysfzjjgProperties xmysfzjjgProperties;
    
    @Autowired
    private ZhbgProperties zhbgProperties;
    
    @Autowired
    private NettyProperties nettyProperties;
    
    /**
     * 测试配置是否正确加载
     */
    @GetMapping("/config")
    public Map<String, Object> testConfig() {
        Map<String, Object> result = new HashMap<>();
        
        // 测试基础配置
        result.put("ip", xmysfzjjgProperties.getIp());
        result.put("port", xmysfzjjgProperties.getPort());
        result.put("wgPort", xmysfzjjgProperties.getWgPort());
        result.put("httpPort", xmysfzjjgProperties.getHttpPort());
        
        // 测试综合办公配置
        Map<String, Object> zhbgConfig = new HashMap<>();
        zhbgConfig.put("postUrl", zhbgProperties.getPostUrl());
        zhbgConfig.put("url30001", zhbgProperties.getUrl30001());
        zhbgConfig.put("url20006", zhbgProperties.getUrl20006());
        result.put("zhbg", zhbgConfig);
        
        // 测试Netty配置
        Map<String, Object> nettyConfig = new HashMap<>();
        nettyConfig.put("socketPort", nettyProperties.getSocketPort());
        nettyConfig.put("httpPort", nettyProperties.getHttpPort());
        nettyConfig.put("workerThreads", nettyProperties.getWorkerThreads());
        result.put("netty", nettyConfig);
        
        return result;
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "ok");
        result.put("service", "xmysfzjjg");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    /**
     * 测试ConfigService
     */
    @GetMapping("/config-service")
    public Map<String, Object> testConfigService() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("IP", configService.getXmysfzjjgValue("IP"));
        result.put("PORT", configService.getXmysfzjjgValue("PORT"));
        result.put("zhbgPostUrl", configService.getZhbgValue("zhbgPostUrl"));
        result.put("url30001", configService.getZhbgValue("url30001"));
        
        return result;
    }
}
