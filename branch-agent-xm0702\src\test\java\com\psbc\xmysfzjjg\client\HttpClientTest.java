package com.psbc.xmysfzjjg.client;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * HTTP客户端测试工具
 * 用于测试REST API接口
 */
public class HttpClientTest {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpClientTest.class);
    
    private static final String BASE_URL = "http://localhost:6666";
    
    public static void main(String[] args) {
        testHttpApis();
    }
    
    public static void testHttpApis() {
        // 测试健康检查
        testHealthCheck();
        
        // 测试配置接口
        testConfigApi();
        
        // 测试综合办公接口
        testZhbgApis();
    }
    
    private static void testHealthCheck() {
        try {
            logger.info("=== 测试健康检查接口 ===");
            
            HttpResponse response = HttpRequest.get(BASE_URL + "/test/health")
                    .execute();
            
            logger.info("健康检查响应状态: {}", response.getStatus());
            logger.info("健康检查响应内容: {}", response.body());
            
        } catch (Exception e) {
            logger.error("健康检查测试失败", e);
        }
    }
    
    private static void testConfigApi() {
        try {
            logger.info("=== 测试配置接口 ===");
            
            HttpResponse response = HttpRequest.get(BASE_URL + "/test/config")
                    .execute();
            
            logger.info("配置接口响应状态: {}", response.getStatus());
            logger.info("配置接口响应内容: {}", response.body());
            
            // 测试ConfigService接口
            response = HttpRequest.get(BASE_URL + "/test/config-service")
                    .execute();
            
            logger.info("ConfigService接口响应状态: {}", response.getStatus());
            logger.info("ConfigService接口响应内容: {}", response.body());
            
        } catch (Exception e) {
            logger.error("配置接口测试失败", e);
        }
    }
    
    private static void testZhbgApis() {
        try {
            logger.info("=== 测试综合办公接口 ===");
            
            // 测试30001接口（JSON）
            String jsonRequest = "{\"serviceno\":\"30001\",\"data\":\"test request\"}";
            
            HttpResponse response = HttpRequest.post(BASE_URL + "/api/admin/xzp/queryYxjfLsxdye")
                    .header("Content-Type", "application/json")
                    .body(jsonRequest)
                    .execute();
            
            logger.info("30001接口响应状态: {}", response.getStatus());
            logger.info("30001接口响应内容: {}", response.body());
            
            // 测试20006接口（XML）
            String xmlRequest = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<content><head><serviceno>20006</serviceno></head>" +
                    "<body><data>test request</data></body></content>";
            
            response = HttpRequest.post(BASE_URL + "/api/admin/xzp/queryJgzhInfo")
                    .header("Content-Type", "application/xml")
                    .body(xmlRequest)
                    .execute();
            
            logger.info("20006接口响应状态: {}", response.getStatus());
            logger.info("20006接口响应内容: {}", response.body());
            
            // 测试健康检查
            response = HttpRequest.get(BASE_URL + "/api/admin/xzp/health")
                    .execute();
            
            logger.info("综合办公健康检查响应状态: {}", response.getStatus());
            logger.info("综合办公健康检查响应内容: {}", response.body());
            
        } catch (Exception e) {
            logger.error("综合办公接口测试失败", e);
        }
    }
}
