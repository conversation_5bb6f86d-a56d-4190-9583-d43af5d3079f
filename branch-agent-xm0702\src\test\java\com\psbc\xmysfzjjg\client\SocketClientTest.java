package com.psbc.xmysfzjjg.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;

/**
 * Socket客户端测试工具
 * 用于测试Netty Socket服务器
 */
public class SocketClientTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SocketClientTest.class);
    
    public static void main(String[] args) {
        testSocketConnection();
    }
    
    public static void testSocketConnection() {
        String host = "127.0.0.1";
        int port = 8888;
        
        Socket socket = null;
        OutputStream os = null;
        InputStream is = null;
        
        try {
            socket = new Socket(host, port);
            os = socket.getOutputStream();
            is = socket.getInputStream();
            
            // 测试JSON消息
            String jsonMessage = "{\"serviceno\":\"30001\",\"data\":\"test json message\"}";
            testMessage(os, is, jsonMessage, "JSON");
            
            Thread.sleep(1000);
            
            // 测试XML消息
            String xmlMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<content><head><serviceno>20006</serviceno></head>" +
                    "<body><data>test xml message</data></body></content>";
            testMessage(os, is, xmlMessage, "XML");
            
            Thread.sleep(1000);
            
            // 测试普通消息（发往委托方）
            String normalMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<content><head><serviceno>other</serviceno></head>" +
                    "<body><data>test normal message</data></body></content>";
            testMessage(os, is, normalMessage, "Normal");
            
        } catch (Exception e) {
            logger.error("Socket测试异常", e);
        } finally {
            closeQuietly(is);
            closeQuietly(os);
            closeQuietly(socket);
        }
    }
    
    private static void testMessage(OutputStream os, InputStream is, String message, String type) {
        try {
            logger.info("发送{}消息: {}", type, message);

            // 按照原有格式发送：6位长度 + 消息内容
            byte[] messageBytes = message.getBytes("UTF-8");
            String lengthStr = String.format("%06d", messageBytes.length);
            String fullMessage = lengthStr + message;

            os.write(fullMessage.getBytes("UTF-8"));
            os.flush();

            // 读取响应头（6字节长度）
            byte[] lengthBuffer = new byte[6];
            int lengthBytesRead = is.read(lengthBuffer);

            if (lengthBytesRead == 6) {
                String responseLengthStr = new String(lengthBuffer).trim();
                int responseLength = Integer.parseInt(responseLengthStr);

                // 读取响应体
                byte[] responseBuffer = new byte[responseLength];
                int responseBytesRead = is.read(responseBuffer);

                if (responseBytesRead == responseLength) {
                    String response = new String(responseBuffer, "UTF-8");
                    logger.info("收到{}响应: {}", type, response);
                } else {
                    logger.warn("响应体长度不匹配，期望: {}, 实际: {}", responseLength, responseBytesRead);
                }
            } else {
                logger.warn("没有收到完整的{}响应头", type);
            }

        } catch (Exception e) {
            logger.error("测试{}消息时发生异常", type, e);
        }
    }
    
    private static void closeQuietly(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                // 忽略关闭异常
            }
        }
    }
    
    private static void closeQuietly(Socket socket) {
        if (socket != null) {
            try {
                socket.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
        }
    }
}
