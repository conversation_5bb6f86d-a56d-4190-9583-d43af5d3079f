package com.psbc.xmysfzjjg.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;

/**
 * Socket客户端测试工具
 * 用于测试Netty Socket服务器
 */
public class SocketClientTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SocketClientTest.class);
    
    public static void main(String[] args) {
        testSocketConnection();
    }
    
    public static void testSocketConnection() {
        String host = "127.0.0.1";
        int port = 8888;

//        // 测试JSON消息 - 使用独立连接
//        String jsonMessage = "{\"serviceno\":\"30001\",\"data\":\"test json message\"}";
//        testSingleMessage(host, port, jsonMessage, "JSON");
//
//        // 等待一下
//        try { Thread.sleep(2000); } catch (InterruptedException e) { /* ignore */ }
//
//        // 测试XML消息 - 使用独立连接
//        String xmlMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
//                "<content><head><serviceno>20006</serviceno></head>" +
//                "<body><data>test xml message</data></body></content>";
//        testSingleMessage(host, port, xmlMessage, "XML");
//
//        // 等待一下
//        try { Thread.sleep(2000); } catch (InterruptedException e) { /* ignore */ }

        // 测试普通消息（发往委托方）- 使用独立连接
        String normalMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>20001</serviceno></head>" +
                "<body><data>test normal message</data></body></content>";
        testSingleMessage(host, port, normalMessage, "Normal");
    }

    /**
     * 测试单个消息，使用独立的Socket连接
     */
    private static void testSingleMessage(String host, int port, String message, String type) {
        Socket socket = null;
        OutputStream os = null;
        InputStream is = null;

        try {
            logger.info("=== 开始测试{}消息 ===", type);
            socket = new Socket(host, port);
            os = socket.getOutputStream();
            is = socket.getInputStream();

            testMessage(os, is, message, type);

        } catch (Exception e) {
            logger.error("测试{}消息时发生异常", type, e);
        } finally {
            closeQuietly(is);
            closeQuietly(os);
            closeQuietly(socket);
            logger.info("=== {}消息测试完成 ===", type);
        }
    }
    
    private static void testMessage(OutputStream os, InputStream is, String message, String type) {
        try {
            logger.info("发送{}消息: {}", type, message);

            // 按照原有格式发送：6位长度 + 消息内容
            byte[] messageBytes = message.getBytes("UTF-8");
            String lengthStr = String.format("%06d", messageBytes.length);
            String fullMessage = lengthStr + message;

            logger.info("发送完整消息: {}", fullMessage);
            os.write(fullMessage.getBytes("UTF-8"));
            os.flush();

            // 等待一下让服务器处理
            Thread.sleep(500);

            // 读取响应，使用更灵活的方式
            byte[] buffer = new byte[4096];
            int totalBytesRead = 0;
            int bytesRead;

            // 设置读取超时
            long startTime = System.currentTimeMillis();
            long timeout = 5000; // 5秒超时

            while (totalBytesRead < 6 && (System.currentTimeMillis() - startTime) < timeout) {
                if (is.available() > 0) {
                    bytesRead = is.read(buffer, totalBytesRead, buffer.length - totalBytesRead);
                    if (bytesRead > 0) {
                        totalBytesRead += bytesRead;
                    }
                } else {
                    Thread.sleep(100);
                }
            }

            if (totalBytesRead >= 6) {
                // 解析长度
                String responseLengthStr = new String(buffer, 0, 6).trim();
                logger.info("响应长度字符串: '{}'", responseLengthStr);

                try {
                    int responseLength = Integer.parseInt(responseLengthStr);

                    // 继续读取响应体
                    while (totalBytesRead < (6 + responseLength) && (System.currentTimeMillis() - startTime) < timeout) {
                        if (is.available() > 0) {
                            bytesRead = is.read(buffer, totalBytesRead, buffer.length - totalBytesRead);
                            if (bytesRead > 0) {
                                totalBytesRead += bytesRead;
                            }
                        } else {
                            Thread.sleep(100);
                        }
                    }

                    if (totalBytesRead >= (6 + responseLength)) {
                        String response = new String(buffer, 6, responseLength, "UTF-8");
                        logger.info("收到{}响应: {}", type, response);
                    } else {
                        logger.warn("响应体读取不完整，期望: {}, 实际: {}", responseLength, totalBytesRead - 6);
                    }

                } catch (NumberFormatException e) {
                    logger.error("无法解析响应长度: '{}'", responseLengthStr);
                }
            } else {
                logger.warn("没有收到完整的{}响应，总共读取: {} 字节", type, totalBytesRead);
                if (totalBytesRead > 0) {
                    String partialResponse = new String(buffer, 0, totalBytesRead, "UTF-8");
                    logger.info("部分响应内容: '{}'", partialResponse);
                }
            }

        } catch (Exception e) {
            logger.error("测试{}消息时发生异常", type, e);
        }
    }
    
    private static void closeQuietly(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                // 忽略关闭异常
            }
        }
    }
    
    private static void closeQuietly(Socket socket) {
        if (socket != null) {
            try {
                socket.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
        }
    }
}
