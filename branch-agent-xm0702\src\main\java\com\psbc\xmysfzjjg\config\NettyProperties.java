package com.psbc.xmysfzjjg.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Netty服务器配置属性类
 */
@Component
@ConfigurationProperties(prefix = "xmysfzjjg.netty")
public class NettyProperties {
    
    /**
     * Socket服务端口
     */
    private Integer socketPort = 8888;
    
    /**
     * HTTP服务端口
     */
    private Integer httpPort = 9999;
    
    /**
     * 工作线程数
     */
    private Integer workerThreads = 10;
    
    /**
     * 连接超时时间(秒)
     */
    private Integer connectTimeout = 30;

    // Getters and Setters
    public Integer getSocketPort() {
        return socketPort;
    }

    public void setSocketPort(Integer socketPort) {
        this.socketPort = socketPort;
    }

    public Integer getHttpPort() {
        return httpPort;
    }

    public void setHttpPort(Integer httpPort) {
        this.httpPort = httpPort;
    }

    public Integer getWorkerThreads() {
        return workerThreads;
    }

    public void setWorkerThreads(Integer workerThreads) {
        this.workerThreads = workerThreads;
    }

    public Integer getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
}
