//package com.psbc.cpufp.service.impl;
//
//import com.psbc.cpufp.mapper.mapperone.GetOneMapper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.Map;
//
///**
// * 数据源1
// *
// * <AUTHOR>
// */
//@Service
//public class GetOneServiceImpl {
//    /**
//     * mapper
//     */
//    @Autowired
//    private GetOneMapper getOneMapper;
//
//    public String queryTxName(String frontTxCode) {
//        return getOneMapper.selectTxName(frontTxCode);
//    }
//
//    public Map<String, String> queryMulti(String frontTxCode, String flag) {
//        return getOneMapper.selectUniteMultiple(frontTxCode, flag);
//    }
//}
