package com.psbc.xmysfzjjg.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;

/**
 * WebService客户端接口
 * 用于调用外部WebService
 */
@WebService(targetNamespace = "http://tempuri.org/")
public interface ClientIWebService {
    
    @WebMethod(operationName = "Execute", action = "http://tempuri.org/Execute")
    @WebResult(name = "ExecuteResult", targetNamespace = "http://tempuri.org/")
    String Execute(@WebParam(targetNamespace = "http://tempuri.org/", name = "BankID") String bankId, 
                   @WebParam(targetNamespace = "http://tempuri.org/", name = "inParmeter") String inParameter);
}
